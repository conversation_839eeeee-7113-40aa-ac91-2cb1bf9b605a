{"name": "pixshop-mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@google/genai": "^1.16.0", "expo": "~53.0.22", "expo-constants": "^17.1.7", "expo-file-system": "^18.1.11", "expo-image-manipulator": "^13.1.7", "expo-image-picker": "^16.1.4", "expo-media-library": "^17.1.7", "expo-secure-store": "~14.2.4", "expo-sharing": "^13.1.5", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-svg": "15.11.2", "expo-router": "~5.1.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "expo-linking": "~7.1.7", "@react-native-async-storage/async-storage": "2.1.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}