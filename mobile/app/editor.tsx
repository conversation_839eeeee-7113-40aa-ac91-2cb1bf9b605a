import React, { useState, useEffect } from 'react';
import { View, StyleSheet, SafeAreaView, Alert } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { EditorCanvas } from '../components/EditorCanvas';
import { Toolbar } from '../components/Toolbar';
import { Header } from '../components/Header';
import { RetouchPanel } from '../components/RetouchPanel';
import { CropPanel } from '../components/CropPanel';
import { AdjustPanel } from '../components/AdjustPanel';
import { FilterPanel } from '../components/FilterPanel';
import { useImageHistory } from '../hooks/useImageHistory';

export type EditingTool = 'retouch' | 'crop' | 'adjust' | 'filters';

export default function EditorScreen() {
  const { imageUri } = useLocalSearchParams<{ imageUri: string }>();
  const [activeTool, setActiveTool] = useState<EditingTool>('retouch');
  const [isLoading, setIsLoading] = useState(false);
  const [hotspot, setHotspot] = useState<{ x: number; y: number } | null>(null);
  
  const {
    currentImage,
    canUndo,
    canRedo,
    undo,
    redo,
    addToHistory,
    reset
  } = useImageHistory(imageUri);

  const handleBack = () => {
    Alert.alert(
      'Discard Changes?',
      'Are you sure you want to go back? All changes will be lost.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Discard', style: 'destructive', onPress: () => router.back() }
      ]
    );
  };

  const handleExport = async () => {
    const { exportImage } = await import('../services/sharingService');
    await exportImage(currentImage);
  };

  const handleImageTap = (x: number, y: number) => {
    if (activeTool === 'retouch') {
      setHotspot({ x, y });
    }
  };

  const handleEditComplete = (newImageUri: string) => {
    addToHistory(newImageUri);
    setHotspot(null);
    setIsLoading(false);
  };

  const handleEditStart = () => {
    setIsLoading(true);
  };

  const handleEditError = (error: string) => {
    setIsLoading(false);
    Alert.alert('Error', error);
  };

  if (!imageUri) {
    return null;
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header
        onBack={handleBack}
        onUndo={canUndo ? undo : undefined}
        onRedo={canRedo ? redo : undefined}
        onExport={handleExport}
        onReset={reset}
        isLoading={isLoading}
      />
      
      <EditorCanvas
        imageUri={currentImage}
        onImageTap={handleImageTap}
        hotspot={hotspot}
        isLoading={isLoading}
      />

      <Toolbar
        activeTool={activeTool}
        onToolChange={setActiveTool}
      />

      {activeTool === 'retouch' && (
        <RetouchPanel
          imageUri={currentImage}
          hotspot={hotspot}
          onEditStart={handleEditStart}
          onEditComplete={handleEditComplete}
          onEditError={handleEditError}
          onClearHotspot={() => setHotspot(null)}
        />
      )}

      {activeTool === 'crop' && (
        <CropPanel
          imageUri={currentImage}
          onEditComplete={handleEditComplete}
          onEditError={handleEditError}
        />
      )}

      {activeTool === 'adjust' && (
        <AdjustPanel
          imageUri={currentImage}
          onEditStart={handleEditStart}
          onEditComplete={handleEditComplete}
          onEditError={handleEditError}
        />
      )}

      {activeTool === 'filters' && (
        <FilterPanel
          imageUri={currentImage}
          onEditStart={handleEditStart}
          onEditComplete={handleEditComplete}
          onEditError={handleEditError}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
});
