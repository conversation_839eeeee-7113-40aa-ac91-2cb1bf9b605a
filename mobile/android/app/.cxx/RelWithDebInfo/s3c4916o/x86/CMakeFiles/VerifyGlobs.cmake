# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# input_SRC at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:47 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/RNEdgeToEdge-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/Props.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/States.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/safeareacontext-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/utils/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:24 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()

# rnsvg_SRCS at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt:24 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/rnsvg.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()

# rnsvg_SRCS at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt:24 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnsvg/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()

# rnsvg_codegen_SRCS at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt:25 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg/*cpp")
set(OLD_GLOB
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp"
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()

# override_cpp_SRC at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:42 (file)
# input_SRC at /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:47 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs")
endif()
