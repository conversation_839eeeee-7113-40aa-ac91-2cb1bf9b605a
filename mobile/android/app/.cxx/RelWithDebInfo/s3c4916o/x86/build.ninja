# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: appmodules
# Configurations: RelWithDebInfo
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = RelWithDebInfo
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/
# =============================================================================
# Object build statements for SHARED_LIBRARY target appmodules


#############################################
# Order-only phony target for appmodules

build cmake_object_order_depends_target_appmodules: phony || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge cmake_object_order_depends_target_react_codegen_rnasyncstorage cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen cmake_object_order_depends_target_react_codegen_rnreanimated cmake_object_order_depends_target_react_codegen_rnscreens cmake_object_order_depends_target_react_codegen_rnsvg cmake_object_order_depends_target_react_codegen_safeareacontext

build CMakeFiles/appmodules.dir/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o: CXX_COMPILER__appmodules_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles/appmodules.dir/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -I/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles/appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles/appmodules.dir/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni
  TARGET_COMPILE_PDB = CMakeFiles/appmodules.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libappmodules.pdb

build CMakeFiles/appmodules.dir/OnLoad.cpp.o: CXX_COMPILER__appmodules_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles/appmodules.dir/OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -I/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles/appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles/appmodules.dir
  TARGET_COMPILE_PDB = CMakeFiles/appmodules.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libappmodules.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target appmodules


#############################################
# Link the shared library /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libappmodules.so

build /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libappmodules.so: CXX_SHARED_LIBRARY_LINKER__appmodules_RelWithDebInfo rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o CMakeFiles/appmodules.dir/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o CMakeFiles/appmodules.dir/OnLoad.cpp.o | /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.so /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_safeareacontext.so /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.so /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/libs/android.x86/libjsi.so /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/libs/android.x86/libreactnative.so || /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.so /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.so /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_safeareacontext.so RNEdgeToEdge_autolinked_build/react_codegen_RNEdgeToEdge rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen rnreanimated_autolinked_build/react_codegen_rnreanimated
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.so  /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_safeareacontext.so  /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.so  /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so  /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/libs/android.x86/libjsi.so  /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/libs/android.x86/libreactnative.so  -latomic -lm
  OBJECT_DIR = CMakeFiles/appmodules.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libappmodules.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles/appmodules.dir/
  TARGET_FILE = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libappmodules.so
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libappmodules.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86 && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86 && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rngesturehandler_codegen


#############################################
# Order-only phony target for react_codegen_rngesturehandler_codegen

build cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen: phony || rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o


#############################################
# Utility command for edit_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/rngesturehandler_codegen_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build rngesturehandler_codegen_autolinked_build/edit_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/rngesturehandler_codegen_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rngesturehandler_codegen_autolinked_build/rebuild_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnreanimated


#############################################
# Order-only phony target for react_codegen_rnreanimated

build cmake_object_order_depends_target_react_codegen_rnreanimated: phony || rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rnreanimated

build rnreanimated_autolinked_build/react_codegen_rnreanimated: phony rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnreanimated_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/rnreanimated_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build rnreanimated_autolinked_build/edit_cache: phony rnreanimated_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/rnreanimated_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnreanimated_autolinked_build/rebuild_cache: phony rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnsvg


#############################################
# Order-only phony target for react_codegen_rnsvg

build cmake_object_order_depends_target_react_codegen_rnsvg: phony || rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnsvg_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o: CXX_COMPILER__react_codegen_rnsvg_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnsvg_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnsvg_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o: CXX_COMPILER__react_codegen_rnsvg_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/rnsvg.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir
  TARGET_COMPILE_PDB = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnsvg_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnsvg_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o: CXX_COMPILER__react_codegen_rnsvg_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnsvg_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o: CXX_COMPILER__react_codegen_rnsvg_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnsvg_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnsvg


#############################################
# Link the shared library /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.so

build /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnsvg_RelWithDebInfo rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o | /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/libs/android.x86/libreactnative.so /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/libs/android.x86/libjsi.so /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/libs/android.x86/libreactnative.so  /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/libs/android.x86/libjsi.so  /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so  -latomic -lm
  OBJECT_DIR = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libreact_codegen_rnsvg.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/
  TARGET_FILE = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.so
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.pdb


#############################################
# Utility command for edit_cache

build rnsvg_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/rnsvg_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build rnsvg_autolinked_build/edit_cache: phony rnsvg_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnsvg_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/rnsvg_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnsvg_autolinked_build/rebuild_cache: phony rnsvg_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Order-only phony target for react_codegen_safeareacontext

build cmake_object_order_depends_target_react_codegen_safeareacontext: phony || safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_safeareacontext.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Link the shared library /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_safeareacontext.so

build /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_safeareacontext.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_safeareacontext_RelWithDebInfo safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o | /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/libs/android.x86/libjsi.so /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/libs/android.x86/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so  /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/libs/android.x86/libjsi.so  /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/libs/android.x86/libreactnative.so  -latomic -lm
  OBJECT_DIR = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libreact_codegen_safeareacontext.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/
  TARGET_FILE = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_safeareacontext.so
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_safeareacontext.pdb


#############################################
# Utility command for edit_cache

build safeareacontext_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/safeareacontext_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build safeareacontext_autolinked_build/edit_cache: phony safeareacontext_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/safeareacontext_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build safeareacontext_autolinked_build/rebuild_cache: phony safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Order-only phony target for react_codegen_rnscreens

build cmake_object_order_depends_target_react_codegen_rnscreens: phony || rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnscreens_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Link the shared library /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.so

build /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnscreens_RelWithDebInfo rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o | /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/libs/android.x86/libreactnative.so /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/libs/android.x86/libjsi.so /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -Wl,--gc-sections
  LINK_LIBRARIES = /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/libs/android.x86/libreactnative.so  /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/libs/android.x86/libjsi.so  /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so  -latomic -lm
  OBJECT_DIR = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir
  POST_BUILD = :
  PRE_LINK = :
  SONAME = libreact_codegen_rnscreens.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/
  TARGET_FILE = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.so
  TARGET_PDB = /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.pdb


#############################################
# Utility command for edit_cache

build rnscreens_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/rnscreens_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build rnscreens_autolinked_build/edit_cache: phony rnscreens_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/rnscreens_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnscreens_autolinked_build/rebuild_cache: phony rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnasyncstorage


#############################################
# Order-only phony target for react_codegen_rnasyncstorage

build cmake_object_order_depends_target_react_codegen_rnasyncstorage: phony || rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rnasyncstorage

build rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/rnasyncstorage_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build rnasyncstorage_autolinked_build/edit_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/rnasyncstorage_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnasyncstorage_autolinked_build/rebuild_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNEdgeToEdge


#############################################
# Order-only phony target for react_codegen_RNEdgeToEdge

build cmake_object_order_depends_target_react_codegen_RNEdgeToEdge: phony || RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/RNEdgeToEdge-generated.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/Props.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_RelWithDebInfo /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/States.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include
  OBJECT_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/
  TARGET_PDB = ""



#############################################
# Object library react_codegen_RNEdgeToEdge

build RNEdgeToEdge_autolinked_build/react_codegen_RNEdgeToEdge: phony RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o


#############################################
# Utility command for edit_cache

build RNEdgeToEdge_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/RNEdgeToEdge_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build RNEdgeToEdge_autolinked_build/edit_cache: phony RNEdgeToEdge_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNEdgeToEdge_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/RNEdgeToEdge_autolinked_build && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -B/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNEdgeToEdge_autolinked_build/rebuild_cache: phony RNEdgeToEdge_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build appmodules: phony /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libappmodules.so

build libappmodules.so: phony /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libappmodules.so

build libreact_codegen_rnscreens.so: phony /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.so

build libreact_codegen_rnsvg.so: phony /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.so

build libreact_codegen_safeareacontext.so: phony /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_safeareacontext.so

build react_codegen_RNEdgeToEdge: phony RNEdgeToEdge_autolinked_build/react_codegen_RNEdgeToEdge

build react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

build react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

build react_codegen_rnreanimated: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

build react_codegen_rnscreens: phony /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.so

build react_codegen_rnsvg: phony /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.so

build react_codegen_safeareacontext: phony /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_safeareacontext.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86

build all: phony /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libappmodules.so rngesturehandler_codegen_autolinked_build/all rnreanimated_autolinked_build/all rnsvg_autolinked_build/all safeareacontext_autolinked_build/all rnscreens_autolinked_build/all rnasyncstorage_autolinked_build/all RNEdgeToEdge_autolinked_build/all

# =============================================================================

#############################################
# Folder: /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/RNEdgeToEdge_autolinked_build

build RNEdgeToEdge_autolinked_build/all: phony RNEdgeToEdge_autolinked_build/react_codegen_RNEdgeToEdge

# =============================================================================

#############################################
# Folder: /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/rnasyncstorage_autolinked_build

build rnasyncstorage_autolinked_build/all: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

# =============================================================================

#############################################
# Folder: /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/rngesturehandler_codegen_autolinked_build

build rngesturehandler_codegen_autolinked_build/all: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

# =============================================================================

#############################################
# Folder: /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/rnreanimated_autolinked_build

build rnreanimated_autolinked_build/all: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

# =============================================================================

#############################################
# Folder: /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/rnscreens_autolinked_build

build rnscreens_autolinked_build/all: phony /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnscreens.so

# =============================================================================

#############################################
# Folder: /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/rnsvg_autolinked_build

build rnsvg_autolinked_build/all: phony /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_rnsvg.so

# =============================================================================

#############################################
# Folder: /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/safeareacontext_autolinked_build

build safeareacontext_autolinked_build/all: phony /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86/libreact_codegen_safeareacontext.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/cmake.verify_globs | /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/abis.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/flags.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/platforms.cmake /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfig.cmake /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfigVersion.cmake /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/VerifyGlobs.cmake /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake /Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/abis.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/flags.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/platforms.cmake /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/prefab/x86/prefab/lib/i686-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfig.cmake /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/prefab/x86/prefab/lib/i686-linux-android/cmake/fbjni/fbjniConfigVersion.cmake /Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86/CMakeFiles/VerifyGlobs.cmake /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake /Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
