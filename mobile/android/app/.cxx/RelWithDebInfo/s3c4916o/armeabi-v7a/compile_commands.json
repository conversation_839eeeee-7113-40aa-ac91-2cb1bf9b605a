[{"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dappmodules_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -I/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles/appmodules.dir/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dappmodules_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -I/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/generated/autolinking/src/main/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles/appmodules.dir/OnLoad.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/rnsvg.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/rnsvg.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/RNEdgeToEdge-generated.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/RNEdgeToEdge-generated.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/Props.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/Props.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp"}, {"directory": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -I/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem /Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o -c /Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/States.cpp", "file": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/States.cpp"}]