                        -H/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/armeabi-v7a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-DCMAKE_FIND_ROOT_PATH=/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/prefab/armeabi-v7a/prefab
-B/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/armeabi-v7a
-GNinja
-DPROJECT_BUILD_DIR=/Users/<USER>/go/src/app/image-editor/mobile/android/app/build
-DPROJECT_ROOT_DIR=/Users/<USER>/go/src/app/image-editor/mobile/android
-DREACT_ANDROID_DIR=/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid
-DANDROID_STL=c++_shared
-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON
                        Build command args: []
                        Version: 2