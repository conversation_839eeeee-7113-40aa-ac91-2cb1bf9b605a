{"enabled": true, "prefabPath": "/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.prefab/cli/2.1.0/aa32fec809c44fa531f01dcfb739b5b3304d3050/cli-2.1.0-all.jar", "packages": ["/Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab", "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/intermediates/prefab_package/release/prefab", "/Users/<USER>/.gradle/caches/8.13/transforms/41bff831db8378bc4365744b809d6eab/transformed/hermes-android-0.79.5-release/prefab", "/Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab"]}