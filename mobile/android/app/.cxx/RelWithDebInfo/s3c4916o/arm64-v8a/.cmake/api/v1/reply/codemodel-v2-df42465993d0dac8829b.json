{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7], "jsonFile": "directory-.-RelWithDebInfo-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-RelWithDebInfo-472b0c94de27a456527a.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-RelWithDebInfo-eea1ca86133b7a769def.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "rnsvg_autolinked_build", "jsonFile": "directory-rnsvg_autolinked_build-RelWithDebInfo-6edc63e8174c8903fef2.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni", "targetIndexes": [6]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-RelWithDebInfo-e35a458e8739ae56bd6a.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [7]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-RelWithDebInfo-b3805ccf3100982cbca7.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [5]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-RelWithDebInfo-ebe4498db2e8411b9c47.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "RNEdgeToEdge_autolinked_build", "jsonFile": "directory-RNEdgeToEdge_autolinked_build-RelWithDebInfo-0165bfd267ad23c1737a.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni", "targetIndexes": [1]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-RelWithDebInfo-67f1806b18b828537fa6.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_RNEdgeToEdge::@668218a48b60eb28aa9e", "jsonFile": "target-react_codegen_RNEdgeToEdge-RelWithDebInfo-4418d30ae78641c469cf.json", "name": "react_codegen_RNEdgeToEdge", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-RelWithDebInfo-310470b306b6833698d3.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-RelWithDebInfo-a61360e60f15a2637765.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-RelWithDebInfo-37364cd76168ca51b9e2.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-RelWithDebInfo-25266ee9578f4f439725.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65", "jsonFile": "target-react_codegen_rnsvg-RelWithDebInfo-5f5337a705e73d73650c.json", "name": "react_codegen_rnsvg", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-RelWithDebInfo-5ad4da3333519bb20a64.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/arm64-v8a", "source": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}