{"buildFiles": ["/Users/<USER>/go/src/app/image-editor/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt", "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt", "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt", "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86_64", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/go/src/app/image-editor/mobile/android/app/.cxx/RelWithDebInfo/s3c4916o/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "appmodules", "output": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86_64/libappmodules.so", "runtimeFiles": ["/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86_64/libreact_codegen_rnsvg.so", "/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86_64/libreact_codegen_safeareacontext.so", "/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86_64/libreact_codegen_rnscreens.so", "/Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86_64/libfbjni.so", "/Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/libs/android.x86_64/libjsi.so", "/Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/libs/android.x86_64/libreactnative.so"]}, "react_codegen_RNEdgeToEdge::@668218a48b60eb28aa9e": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_RNEdgeToEdge"}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rnasyncstorage"}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rngesturehandler_codegen"}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rnreanimated"}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rnscreens", "output": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86_64/libreact_codegen_rnscreens.so", "runtimeFiles": ["/Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/libs/android.x86_64/libreactnative.so", "/Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/libs/android.x86_64/libjsi.so", "/Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86_64/libfbjni.so"]}, "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rnsvg", "output": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86_64/libreact_codegen_rnsvg.so", "runtimeFiles": ["/Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/libs/android.x86_64/libreactnative.so", "/Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/libs/android.x86_64/libjsi.so", "/Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86_64/libfbjni.so"]}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_safeareacontext", "output": "/Users/<USER>/go/src/app/image-editor/mobile/android/app/build/intermediates/cxx/RelWithDebInfo/s3c4916o/obj/x86_64/libreact_codegen_safeareacontext.so", "runtimeFiles": ["/Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86_64/libfbjni.so", "/Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/jsi/libs/android.x86_64/libjsi.so", "/Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/prefab/modules/reactnative/libs/android.x86_64/libreactnative.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang.lld", "cppCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}