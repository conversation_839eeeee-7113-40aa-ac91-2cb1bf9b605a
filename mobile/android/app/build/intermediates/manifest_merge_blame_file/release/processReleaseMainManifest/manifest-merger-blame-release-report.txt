1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.anonymous.pixshopmobile"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.CAMERA" />
11-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:2:3-62
11-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:2:20-60
12    <uses-permission android:name="android.permission.INTERNET" />
12-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:3:3-64
12-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:3:20-62
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:4:3-77
13-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:4:20-75
14    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
14-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:5:3-73
14-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:5:20-71
15    <uses-permission android:name="android.permission.RECORD_AUDIO" />
15-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:6:3-68
15-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:6:20-66
16    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
16-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:7:3-75
16-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:7:20-73
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:8:3-63
17-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:8:20-61
18    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
18-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:9:3-78
18-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:9:20-76
19
20    <queries>
20-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:10:3-16:13
21        <intent>
21-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:11:5-15:14
22            <action android:name="android.intent.action.VIEW" />
22-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:12:7-58
22-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:12:15-56
23
24            <category android:name="android.intent.category.BROWSABLE" />
24-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:13:7-67
24-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:13:17-65
25
26            <data android:scheme="https" />
26-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:14:7-37
26-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:14:13-35
27        </intent>
28        <!-- Query open documents -->
29        <intent>
29-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:15:9-17:18
30            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
30-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:16:13-79
30-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:16:21-76
31        </intent>
32        <intent>
32-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:15:9-19:18
33
34            <!-- Required for picking images from the camera roll if targeting API 30 -->
35            <action android:name="android.media.action.IMAGE_CAPTURE" />
35-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:18:13-73
35-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:18:21-70
36        </intent>
37        <intent>
37-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:20:9-24:18
38
39            <!-- Required for picking images from the camera if targeting API 30 -->
40            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
40-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:23:13-80
40-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:23:21-77
41        </intent>
42        <intent>
42-->[host.exp.exponent:expo.modules.sharing:13.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/a9bfde12bc44e3e6412c81c9908b63f2/transformed/expo.modules.sharing-13.1.5/AndroidManifest.xml:8:9-14:18
43
44            <!-- Required for file sharing if targeting API 30 -->
45            <action android:name="android.intent.action.SEND" />
45-->[host.exp.exponent:expo.modules.sharing:13.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/a9bfde12bc44e3e6412c81c9908b63f2/transformed/expo.modules.sharing-13.1.5/AndroidManifest.xml:11:13-65
45-->[host.exp.exponent:expo.modules.sharing:13.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/a9bfde12bc44e3e6412c81c9908b63f2/transformed/expo.modules.sharing-13.1.5/AndroidManifest.xml:11:21-62
46
47            <data android:mimeType="*/*" />
47-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:14:7-37
48        </intent>
49        <intent>
49-->[com.vanniktech:android-image-cropper:4.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/22c05e568ac5d149f07f1e99d719b136/transformed/android-image-cropper-4.6.0/AndroidManifest.xml:8:9-14:18
50            <action android:name="android.intent.action.GET_CONTENT" />
50-->[com.vanniktech:android-image-cropper:4.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/22c05e568ac5d149f07f1e99d719b136/transformed/android-image-cropper-4.6.0/AndroidManifest.xml:9:13-72
50-->[com.vanniktech:android-image-cropper:4.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/22c05e568ac5d149f07f1e99d719b136/transformed/android-image-cropper-4.6.0/AndroidManifest.xml:9:21-69
51
52            <category android:name="android.intent.category.OPENABLE" />
52-->[com.vanniktech:android-image-cropper:4.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/22c05e568ac5d149f07f1e99d719b136/transformed/android-image-cropper-4.6.0/AndroidManifest.xml:11:13-73
52-->[com.vanniktech:android-image-cropper:4.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/22c05e568ac5d149f07f1e99d719b136/transformed/android-image-cropper-4.6.0/AndroidManifest.xml:11:23-70
53
54            <data android:mimeType="*/*" />
54-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:14:7-37
55        </intent>
56    </queries>
57
58    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
58-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/a2cbb89982dd856421bd2da7e11e062a/transformed/expo.modules.medialibrary-17.1.7/AndroidManifest.xml:8:5-75
58-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/a2cbb89982dd856421bd2da7e11e062a/transformed/expo.modules.medialibrary-17.1.7/AndroidManifest.xml:8:22-72
59    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
59-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/a2cbb89982dd856421bd2da7e11e062a/transformed/expo.modules.medialibrary-17.1.7/AndroidManifest.xml:9:5-75
59-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/a2cbb89982dd856421bd2da7e11e062a/transformed/expo.modules.medialibrary-17.1.7/AndroidManifest.xml:9:22-72
60    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
60-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/a2cbb89982dd856421bd2da7e11e062a/transformed/expo.modules.medialibrary-17.1.7/AndroidManifest.xml:10:5-90
60-->[host.exp.exponent:expo.modules.medialibrary:17.1.7] /Users/<USER>/.gradle/caches/8.13/transforms/a2cbb89982dd856421bd2da7e11e062a/transformed/expo.modules.medialibrary-17.1.7/AndroidManifest.xml:10:22-87
61    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
61-->[androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/88b9f6ad7ed87d7d30486cdd2fcbb5f7/transformed/biometric-1.1.0/AndroidManifest.xml:24:5-72
61-->[androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/88b9f6ad7ed87d7d30486cdd2fcbb5f7/transformed/biometric-1.1.0/AndroidManifest.xml:24:22-69
62    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
62-->[androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/88b9f6ad7ed87d7d30486cdd2fcbb5f7/transformed/biometric-1.1.0/AndroidManifest.xml:27:5-74
62-->[androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.13/transforms/88b9f6ad7ed87d7d30486cdd2fcbb5f7/transformed/biometric-1.1.0/AndroidManifest.xml:27:22-71
63
64    <permission
64-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
65        android:name="com.anonymous.pixshopmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
65-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
66        android:protectionLevel="signature" />
66-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
67
68    <uses-permission android:name="com.anonymous.pixshopmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
68-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
68-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
69
70    <application
70-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:17:3-33:17
71        android:name="com.anonymous.pixshopmobile.MainApplication"
71-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:17:16-47
72        android:allowBackup="true"
72-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:17:162-188
73        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
73-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
74        android:dataExtractionRules="@xml/secure_store_data_extraction_rules"
74-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:17:307-376
75        android:extractNativeLibs="false"
76        android:fullBackupContent="@xml/secure_store_backup_rules"
76-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:17:248-306
77        android:icon="@mipmap/ic_launcher"
77-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:17:81-115
78        android:label="@string/app_name"
78-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:17:48-80
79        android:requestLegacyExternalStorage="true"
79-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:17:377-420
80        android:roundIcon="@mipmap/ic_launcher_round"
80-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:17:116-161
81        android:supportsRtl="true"
81-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:17:221-247
82        android:theme="@style/AppTheme" >
82-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:17:189-220
83        <meta-data
83-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:18:5-83
84            android:name="expo.modules.updates.ENABLED"
84-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:18:16-59
85            android:value="false" />
85-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:18:60-81
86        <meta-data
86-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:19:5-105
87            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
87-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:19:16-80
88            android:value="ALWAYS" />
88-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:19:81-103
89        <meta-data
89-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:20:5-99
90            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
90-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:20:16-79
91            android:value="0" />
91-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:20:80-97
92
93        <activity
93-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:21:5-32:16
94            android:name="com.anonymous.pixshopmobile.MainActivity"
94-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:21:15-43
95            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
95-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:21:44-134
96            android:exported="true"
96-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:21:256-279
97            android:launchMode="singleTask"
97-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:21:135-166
98            android:screenOrientation="portrait"
98-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:21:280-316
99            android:theme="@style/Theme.App.SplashScreen"
99-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:21:210-255
100            android:windowSoftInputMode="adjustResize" >
100-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:21:167-209
101            <intent-filter>
101-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:22:7-25:23
102                <action android:name="android.intent.action.MAIN" />
102-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:23:9-60
102-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:23:17-58
103
104                <category android:name="android.intent.category.LAUNCHER" />
104-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:24:9-68
104-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:24:19-66
105            </intent-filter>
106            <intent-filter>
106-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:26:7-31:23
107                <action android:name="android.intent.action.VIEW" />
107-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:12:7-58
107-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:12:15-56
108
109                <category android:name="android.intent.category.DEFAULT" />
109-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:28:9-67
109-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:28:19-65
110                <category android:name="android.intent.category.BROWSABLE" />
110-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:13:7-67
110-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:13:17-65
111
112                <data android:scheme="pixshop" />
112-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:14:7-37
112-->/Users/<USER>/go/src/app/image-editor/mobile/android/app/src/main/AndroidManifest.xml:14:13-35
113            </intent-filter>
114        </activity>
115
116        <meta-data
116-->[:expo-modules-core] /Users/<USER>/go/src/app/image-editor/mobile/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:9:9-11:89
117            android:name="org.unimodules.core.AppLoader#react-native-headless"
117-->[:expo-modules-core] /Users/<USER>/go/src/app/image-editor/mobile/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:10:13-79
118            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
118-->[:expo-modules-core] /Users/<USER>/go/src/app/image-editor/mobile/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:11:13-86
119        <meta-data
119-->[:expo-modules-core] /Users/<USER>/go/src/app/image-editor/mobile/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:12:9-15:45
120            android:name="com.facebook.soloader.enabled"
120-->[:expo-modules-core] /Users/<USER>/go/src/app/image-editor/mobile/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:13:13-57
121            android:value="true" />
121-->[:expo-modules-core] /Users/<USER>/go/src/app/image-editor/mobile/node_modules/expo-modules-core/android/build/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml:14:13-33
122
123        <provider
123-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:21:9-30:20
124            android:name="expo.modules.filesystem.FileSystemFileProvider"
124-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:22:13-74
125            android:authorities="com.anonymous.pixshopmobile.FileSystemFileProvider"
125-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:23:13-74
126            android:exported="false"
126-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:24:13-37
127            android:grantUriPermissions="true" >
127-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:25:13-47
128            <meta-data
128-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:27:13-29:70
129                android:name="android.support.FILE_PROVIDER_PATHS"
129-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:28:17-67
130                android:resource="@xml/file_system_provider_paths" />
130-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:29:17-67
131        </provider>
132
133        <service
133-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:28:9-40:19
134            android:name="com.google.android.gms.metadata.ModuleDependencies"
134-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:29:13-78
135            android:enabled="false"
135-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:30:13-36
136            android:exported="false" >
136-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:31:13-37
137            <intent-filter>
137-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:33:13-35:29
138                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
138-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:34:17-94
138-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:34:25-91
139            </intent-filter>
140
141            <meta-data
141-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:37:13-39:36
142                android:name="photopicker_activity:0:required"
142-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:38:17-63
143                android:value="" />
143-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:39:17-33
144        </service>
145
146        <activity
146-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:42:9-44:59
147            android:name="com.canhub.cropper.CropImageActivity"
147-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:43:13-64
148            android:exported="true"
148-->[com.vanniktech:android-image-cropper:4.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/22c05e568ac5d149f07f1e99d719b136/transformed/android-image-cropper-4.6.0/AndroidManifest.xml:33:13-36
149            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
149-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:44:13-56
150        <provider
150-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:46:9-54:20
151            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
151-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:47:13-89
152            android:authorities="com.anonymous.pixshopmobile.ImagePickerFileProvider"
152-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:48:13-75
153            android:exported="false"
153-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:49:13-37
154            android:grantUriPermissions="true" >
154-->[host.exp.exponent:expo.modules.imagepicker:16.1.4] /Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/AndroidManifest.xml:50:13-47
155            <meta-data
155-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:27:13-29:70
156                android:name="android.support.FILE_PROVIDER_PATHS"
156-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:28:17-67
157                android:resource="@xml/image_picker_provider_paths" />
157-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:29:17-67
158        </provider>
159        <provider
159-->[host.exp.exponent:expo.modules.sharing:13.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/a9bfde12bc44e3e6412c81c9908b63f2/transformed/expo.modules.sharing-13.1.5/AndroidManifest.xml:18:9-26:20
160            android:name="expo.modules.sharing.SharingFileProvider"
160-->[host.exp.exponent:expo.modules.sharing:13.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/a9bfde12bc44e3e6412c81c9908b63f2/transformed/expo.modules.sharing-13.1.5/AndroidManifest.xml:19:13-68
161            android:authorities="com.anonymous.pixshopmobile.SharingFileProvider"
161-->[host.exp.exponent:expo.modules.sharing:13.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/a9bfde12bc44e3e6412c81c9908b63f2/transformed/expo.modules.sharing-13.1.5/AndroidManifest.xml:20:13-71
162            android:exported="false"
162-->[host.exp.exponent:expo.modules.sharing:13.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/a9bfde12bc44e3e6412c81c9908b63f2/transformed/expo.modules.sharing-13.1.5/AndroidManifest.xml:21:13-37
163            android:grantUriPermissions="true" >
163-->[host.exp.exponent:expo.modules.sharing:13.1.5] /Users/<USER>/.gradle/caches/8.13/transforms/a9bfde12bc44e3e6412c81c9908b63f2/transformed/expo.modules.sharing-13.1.5/AndroidManifest.xml:22:13-47
164            <meta-data
164-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:27:13-29:70
165                android:name="android.support.FILE_PROVIDER_PATHS"
165-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:28:17-67
166                android:resource="@xml/sharing_provider_paths" />
166-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:29:17-67
167        </provider>
168        <provider
168-->[com.vanniktech:android-image-cropper:4.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/22c05e568ac5d149f07f1e99d719b136/transformed/android-image-cropper-4.6.0/AndroidManifest.xml:21:9-29:20
169            android:name="com.canhub.cropper.CropFileProvider"
169-->[com.vanniktech:android-image-cropper:4.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/22c05e568ac5d149f07f1e99d719b136/transformed/android-image-cropper-4.6.0/AndroidManifest.xml:22:13-63
170            android:authorities="com.anonymous.pixshopmobile.cropper.fileprovider"
170-->[com.vanniktech:android-image-cropper:4.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/22c05e568ac5d149f07f1e99d719b136/transformed/android-image-cropper-4.6.0/AndroidManifest.xml:23:13-72
171            android:exported="false"
171-->[com.vanniktech:android-image-cropper:4.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/22c05e568ac5d149f07f1e99d719b136/transformed/android-image-cropper-4.6.0/AndroidManifest.xml:24:13-37
172            android:grantUriPermissions="true" >
172-->[com.vanniktech:android-image-cropper:4.6.0] /Users/<USER>/.gradle/caches/8.13/transforms/22c05e568ac5d149f07f1e99d719b136/transformed/android-image-cropper-4.6.0/AndroidManifest.xml:25:13-47
173            <meta-data
173-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:27:13-29:70
174                android:name="android.support.FILE_PROVIDER_PATHS"
174-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:28:17-67
175                android:resource="@xml/library_file_paths" />
175-->[host.exp.exponent:expo.modules.filesystem:18.1.11] /Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/AndroidManifest.xml:29:17-67
176        </provider>
177        <provider
177-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
178            android:name="androidx.startup.InitializationProvider"
178-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
179            android:authorities="com.anonymous.pixshopmobile.androidx-startup"
179-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
180            android:exported="false" >
180-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
181            <meta-data
181-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
182                android:name="androidx.emoji2.text.EmojiCompatInitializer"
182-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
183                android:value="androidx.startup" />
183-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
184            <meta-data
184-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
185                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
185-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
186                android:value="androidx.startup" />
186-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
187            <meta-data
187-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:29:13-31:52
188                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
188-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:30:17-85
189                android:value="androidx.startup" />
189-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:31:17-49
190        </provider>
191
192        <receiver
192-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:34:9-52:20
193            android:name="androidx.profileinstaller.ProfileInstallReceiver"
193-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:35:13-76
194            android:directBootAware="false"
194-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:36:13-44
195            android:enabled="true"
195-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:37:13-35
196            android:exported="true"
196-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:38:13-36
197            android:permission="android.permission.DUMP" >
197-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:39:13-57
198            <intent-filter>
198-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:40:13-42:29
199                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
199-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:41:17-91
199-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:41:25-88
200            </intent-filter>
201            <intent-filter>
201-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:43:13-45:29
202                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
202-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:44:17-85
202-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:44:25-82
203            </intent-filter>
204            <intent-filter>
204-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:46:13-48:29
205                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
205-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:47:17-88
205-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:47:25-85
206            </intent-filter>
207            <intent-filter>
207-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:49:13-51:29
208                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
208-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:50:17-95
208-->[androidx.profileinstaller:profileinstaller:1.4.0] /Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/AndroidManifest.xml:50:25-92
209            </intent-filter>
210        </receiver>
211    </application>
212
213</manifest>
