{"logs": [{"outputFile": "com.anonymous.pixshopmobile.app-mergeReleaseResources-51:/values-uz/values-uz.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/res/values-uz/values-uz.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,360,440,523,617,704,799,926,1010,1070,1134,1237,1307,1374,1483,1546,1613,1672,1746,1809,1863,1978,2036,2098,2152,2227,2356,2446,2526,2619,2703,2792,2933,3015,3097,3236,3322,3406,3466,3517,3583,3656,3734,3805,3886,3958,4035,4110,4181,4282,4376,4455,4551,4645,4719,4795,4881,4934,5021,5087,5172,5263,5325,5389,5452,5521,5623,5724,5820,5921,5985,6040,6123,6209,6286", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,79,82,93,86,94,126,83,59,63,102,69,66,108,62,66,58,73,62,53,114,57,61,53,74,128,89,79,92,83,88,140,81,81,138,85,83,59,50,65,72,77,70,80,71,76,74,70,100,93,78,95,93,73,75,85,52,86,65,84,90,61,63,62,68,101,100,95,100,63,54,82,85,76,73", "endOffsets": "278,355,435,518,612,699,794,921,1005,1065,1129,1232,1302,1369,1478,1541,1608,1667,1741,1804,1858,1973,2031,2093,2147,2222,2351,2441,2521,2614,2698,2787,2928,3010,3092,3231,3317,3401,3461,3512,3578,3651,3729,3800,3881,3953,4030,4105,4176,4277,4371,4450,4546,4640,4714,4790,4876,4929,5016,5082,5167,5258,5320,5384,5447,5516,5618,5719,5815,5916,5980,6035,6118,6204,6281,6355"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,50,51,52,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3004,3081,3161,3244,3338,4161,4256,4383,4671,4731,4795,6218,6288,6355,6464,6527,6594,6653,6727,6790,6844,6959,7017,7079,7133,7208,7337,7427,7507,7600,7684,7773,7914,7996,8078,8217,8303,8387,8447,8498,8564,8637,8715,8786,8867,8939,9016,9091,9162,9263,9357,9436,9532,9626,9700,9776,9862,9915,10002,10068,10153,10244,10306,10370,10433,10502,10604,10705,10801,10902,10966,11021,11188,11274,11351", "endLines": "5,33,34,35,36,37,45,46,47,50,51,52,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,125,126,127", "endColumns": "12,76,79,82,93,86,94,126,83,59,63,102,69,66,108,62,66,58,73,62,53,114,57,61,53,74,128,89,79,92,83,88,140,81,81,138,85,83,59,50,65,72,77,70,80,71,76,74,70,100,93,78,95,93,73,75,85,52,86,65,84,90,61,63,62,68,101,100,95,100,63,54,82,85,76,73", "endOffsets": "328,3076,3156,3239,3333,3420,4251,4378,4462,4726,4790,4893,6283,6350,6459,6522,6589,6648,6722,6785,6839,6954,7012,7074,7128,7203,7332,7422,7502,7595,7679,7768,7909,7991,8073,8212,8298,8382,8442,8493,8559,8632,8710,8781,8862,8934,9011,9086,9157,9258,9352,9431,9527,9621,9695,9771,9857,9910,9997,10063,10148,10239,10301,10365,10428,10497,10599,10700,10796,10897,10961,11016,11099,11269,11346,11420"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/res/values-uz/values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "38,39,40,41,42,43,44,128", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3425,3527,3629,3730,3830,3938,4042,11425", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "3522,3624,3725,3825,3933,4037,4156,11521"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/res/values-uz/values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,438,533,633,715,815,932,1017,1095,1186,1279,1374,1468,1562,1655,1750,1845,1936,2028,2112,2222,2328,2428,2536,2642,2744,2905,11104", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "433,528,628,710,810,927,1012,1090,1181,1274,1369,1463,1557,1650,1745,1840,1931,2023,2107,2217,2323,2423,2531,2637,2739,2900,2999,11183"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/88b9f6ad7ed87d7d30486cdd2fcbb5f7/transformed/biometric-1.1.0/res/values-uz/values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,259,379,510,638,785,917,1062,1159,1298,1438", "endColumns": "113,89,119,130,127,146,131,144,96,138,139,140", "endOffsets": "164,254,374,505,633,780,912,1057,1154,1293,1433,1574"}, "to": {"startLines": "48,49,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4467,4581,4898,5018,5149,5277,5424,5556,5701,5798,5937,6077", "endColumns": "113,89,119,130,127,146,131,144,96,138,139,140", "endOffsets": "4576,4666,5013,5144,5272,5419,5551,5696,5793,5932,6072,6213"}}]}]}