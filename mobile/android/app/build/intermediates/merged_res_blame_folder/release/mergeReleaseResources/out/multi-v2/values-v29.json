{"logs": [{"outputFile": "com.anonymous.pixshopmobile.app-mergeReleaseResources-51:/values-v29/values-v29.xml", "map": [{"source": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-edge-to-edge/android/build/intermediates/packaged_res/release/packageReleaseResources/values-v29/values-v29.xml", "from": {"startLines": "2,8,14,20,26,32,38,44", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,476,877,1318,1739,2180,2637,3074", "endLines": "7,13,19,25,31,37,43,49", "endColumns": "12,12,12,12,12,12,12,12", "endOffsets": "471,872,1313,1734,2175,2632,3069,3490"}}]}]}