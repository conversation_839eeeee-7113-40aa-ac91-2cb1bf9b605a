{"logs": [{"outputFile": "com.anonymous.pixshopmobile.app-mergeReleaseResources-51:/values-en-rGB/values-en-rGB.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/res/values-en-rGB/values-en-rGB.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,777,892,971,1031,1096,1186,1253,1312,1402,1466,1530,1593,1662,1726,1780,1892,1950,2012,2066,2138,2260,2347,2422,2513,2594,2675,2815,2892,2973,3100,3191,3268,3322,3373,3439,3509,3586,3657,3732,3803,3880,3949,4018,4125,4216,4288,4377,4466,4540,4612,4698,4748,4827,4893,4973,5057,5119,5183,5246,5315,5415,5510,5602,5694,5752,5807,5885,5966,6041", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,81,77,76,85,83,97,114,78,59,64,89,66,58,89,63,63,62,68,63,53,111,57,61,53,71,121,86,74,90,80,80,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,77,80,74,74", "endOffsets": "267,349,427,504,590,674,772,887,966,1026,1091,1181,1248,1307,1397,1461,1525,1588,1657,1721,1775,1887,1945,2007,2061,2133,2255,2342,2417,2508,2589,2670,2810,2887,2968,3095,3186,3263,3317,3368,3434,3504,3581,3652,3727,3798,3875,3944,4013,4120,4211,4283,4372,4461,4535,4607,4693,4743,4822,4888,4968,5052,5114,5178,5241,5310,5410,5505,5597,5689,5747,5802,5880,5961,6036,6111"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,51,52,53,64,66,67,68,69,70,71,72,73,74,75,76,77,78,79,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,132,133,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2979,3061,3139,3216,3302,4105,4203,4318,4669,4729,4794,6131,6281,6340,6430,6494,6558,6621,6690,6754,6808,6920,6978,7040,7094,7166,7438,7525,7600,7691,7772,7853,7993,8070,8151,8278,8369,8446,8500,8551,8617,8687,8764,8835,8910,8981,9058,9127,9196,9303,9394,9466,9555,9644,9718,9790,9876,9926,10005,10071,10151,10235,10297,10361,10424,10493,10593,10688,10780,10872,10930,10985,11386,11467,11542", "endLines": "5,33,34,35,36,37,45,46,47,51,52,53,64,66,67,68,69,70,71,72,73,74,75,76,77,78,79,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,132,133,134", "endColumns": "12,81,77,76,85,83,97,114,78,59,64,89,66,58,89,63,63,62,68,63,53,111,57,61,53,71,121,86,74,90,80,80,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,77,80,74,74", "endOffsets": "317,3056,3134,3211,3297,3381,4198,4313,4392,4724,4789,4879,6193,6335,6425,6489,6553,6616,6685,6749,6803,6915,6973,7035,7089,7161,7283,7520,7595,7686,7767,7848,7988,8065,8146,8273,8364,8441,8495,8546,8612,8682,8759,8830,8905,8976,9053,9122,9191,9298,9389,9461,9550,9639,9713,9785,9871,9921,10000,10066,10146,10230,10292,10356,10419,10488,10588,10683,10775,10867,10925,10980,11058,11462,11537,11612"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/res/values-en-rGB/values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,131", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,426,526,634,718,818,933,1011,1086,1177,1270,1365,1459,1559,1652,1747,1841,1932,2023,2105,2208,2311,2410,2515,2619,2723,2879,11303", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "421,521,629,713,813,928,1006,1081,1172,1265,1360,1454,1554,1647,1742,1836,1927,2018,2100,2203,2306,2405,2510,2614,2718,2874,2974,11381"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/88b9f6ad7ed87d7d30486cdd2fcbb5f7/transformed/biometric-1.1.0/res/values-en-rGB/values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,251,370,499,637,764,882,1013,1113,1239,1378", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "156,246,365,494,632,759,877,1008,1108,1234,1373,1493"}, "to": {"startLines": "49,50,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4473,4579,4884,5003,5132,5270,5397,5515,5646,5746,5872,6011", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "4574,4664,4998,5127,5265,5392,5510,5641,5741,5867,6006,6126"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/res/values-en-rGB/values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "38,39,40,41,42,43,44,136", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3386,3482,3584,3683,3782,3886,3989,11697", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3477,3579,3678,3777,3881,3984,4100,11793"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/res/values-en-rGB/values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,214,288,364,446,526,604,684,758", "endColumns": "75,82,73,75,81,79,77,79,73,73", "endOffsets": "126,209,283,359,441,521,599,679,753,827"}, "to": {"startLines": "48,65,80,81,128,129,130,135,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "4397,6198,7288,7362,11063,11145,11225,11617,11798,11872", "endColumns": "75,82,73,75,81,79,77,79,73,73", "endOffsets": "4468,6276,7357,7433,11140,11220,11298,11692,11867,11941"}}]}]}