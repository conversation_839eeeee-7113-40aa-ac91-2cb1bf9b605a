{"logs": [{"outputFile": "com.anonymous.pixshopmobile.app-mergeReleaseResources-51:/values-pt/values-pt.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,211,282,352,435,502,569,648,727,815,908,976,1062,1147,1223,1306,1388,1463,1541,1615,1701,1773,1852,1928", "endColumns": "69,85,70,69,82,66,66,78,78,87,92,67,85,84,75,82,81,74,77,73,85,71,78,75,85", "endOffsets": "120,206,277,347,430,497,564,643,722,810,903,971,1057,1142,1218,1301,1383,1458,1536,1610,1696,1768,1847,1923,2009"}, "to": {"startLines": "29,37,50,51,52,53,54,55,56,57,58,59,60,62,63,64,65,66,67,68,69,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2843,3648,5242,5313,5383,5466,5533,5600,5679,5758,5846,5939,6007,6179,6264,6340,6423,6505,6580,6658,6732,6919,6991,7070,7146", "endColumns": "69,85,70,69,82,66,66,78,78,87,92,67,85,84,75,82,81,74,77,73,85,71,78,75,85", "endOffsets": "2908,3729,5308,5378,5461,5528,5595,5674,5753,5841,5934,6002,6088,6259,6335,6418,6500,6575,6653,6727,6813,6986,7065,7141,7227"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/88b9f6ad7ed87d7d30486cdd2fcbb5f7/transformed/biometric-1.1.0/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,253,370,509,656,787,917,1061,1162,1296,1440", "endColumns": "103,93,116,138,146,130,129,143,100,133,143,122", "endOffsets": "154,248,365,504,651,782,912,1056,1157,1291,1435,1558"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46,47,48,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3734,3838,3932,4049,4188,4335,4466,4596,4740,4841,4975,5119", "endColumns": "103,93,116,138,146,130,129,143,100,133,143,122", "endOffsets": "3833,3927,4044,4183,4330,4461,4591,4735,4836,4970,5114,5237"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,6093", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,6174"}}, {"source": "/Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "30,31,32,33,34,35,36,70", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2913,3010,3112,3211,3311,3418,3528,6818", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3005,3107,3206,3306,3413,3523,3643,6914"}}]}]}