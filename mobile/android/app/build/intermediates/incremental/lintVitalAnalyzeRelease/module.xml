<lint-module
    format="1"
    dir="/Users/<USER>/go/src/app/image-editor/mobile/android/app"
    name=":app"
    type="APP"
    maven="Pixshop Mobile:app:unspecified"
    agpVersion="8.8.2"
    buildFolder="build"
    bootClassPath="/Users/<USER>/Library/Android/sdk/platforms/android-35/android.jar:/Users/<USER>/Library/Android/sdk/build-tools/35.0.0/core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-35"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
