<libraries>
  <library
      name=":@@:react-native-screens::release"
      project=":react-native-screens"/>
  <library
      name=":@@:react-native-edge-to-edge::release"
      project=":react-native-edge-to-edge"/>
  <library
      name="com.facebook.react:react-android:0.79.5:release@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release/jars/classes.jar"
      resolved="com.facebook.react:react-android:0.79.5"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/c0eaab12c39868587e4f18db7dfb16a1/transformed/react-android-0.79.5-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:fresco:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/4585a14f1ae9a66565345740533a24e2/transformed/fresco-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:fresco:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/4585a14f1ae9a66565345740533a24e2/transformed/fresco-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline-okhttp3:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/11cf39d0535535ef53e14603004c5e44/transformed/imagepipeline-okhttp3-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:imagepipeline-okhttp3:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/11cf39d0535535ef53e14603004c5e44/transformed/imagepipeline-okhttp3-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:middleware:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/cfd2bc4667cb4cb0287aae0978df24ac/transformed/middleware-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:middleware:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/cfd2bc4667cb4cb0287aae0978df24ac/transformed/middleware-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:ui-common:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/3356ac95f0115ef5184f7de4471e60d4/transformed/ui-common-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:ui-common:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/3356ac95f0115ef5184f7de4471e60d4/transformed/ui-common-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo::release"
      project=":expo"/>
  <library
      name="host.exp.exponent:expo.modules.filesystem:18.1.11@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11/jars/classes.jar"
      resolved="host.exp.exponent:expo.modules.filesystem:18.1.11"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/35e95e07eb5bf2a9d070da191dbee833/transformed/expo.modules.filesystem-18.1.11"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:okhttp-urlconnection:4.9.2@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.okhttp3/okhttp-urlconnection/4.9.2/3b9e64d3d56370bc7488ed8b336d17a8013cb336/okhttp-urlconnection-4.9.2.jar"
      resolved="com.squareup.okhttp3:okhttp-urlconnection:4.9.2"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.9.2@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.okhttp3/okhttp/4.9.2/5302714ee9320b64cf65ed865e5f65981ef9ba46/okhttp-4.9.2.jar"
      resolved="com.squareup.okhttp3:okhttp:4.9.2"/>
  <library
      name="com.squareup.okio:okio:2.9.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.squareup.okio/okio/2.9.0/dcc813b08ce5933f8bdfd1dfbab4ad4bd170e7a/okio-jvm-2.9.0.jar"
      resolved="com.squareup.okio:okio:2.9.0"/>
  <library
      name=":@@:expo-modules-core::release"
      project=":expo-modules-core"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-core-jvm/1.8.1/bb0e192bd7c2b6b8217440d36e9758e377e450/kotlinx-coroutines-core-jvm-1.8.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/067eb4768d0977cc253e099ab589c211/transformed/appcompat-resources-1.7.0/jars/classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.7.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/067eb4768d0977cc253e099ab589c211/transformed/appcompat-resources-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0/jars/classes.jar"
      resolved="androidx.appcompat:appcompat:1.7.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/af9a9d7ace362c65c6063a55648731b1/transformed/appcompat-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name=":@@:expo-image-loader::release"
      project=":expo-image-loader"/>
  <library
      name="com.github.bumptech.glide:glide:4.16.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/bbeeba403c008876f9fc01e5e82c994b/transformed/glide-4.16.0/jars/classes.jar"
      resolved="com.github.bumptech.glide:glide:4.16.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/bbeeba403c008876f9fc01e5e82c994b/transformed/glide-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.sharing:13.1.5@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/a9bfde12bc44e3e6412c81c9908b63f2/transformed/expo.modules.sharing-13.1.5/jars/classes.jar"
      resolved="host.exp.exponent:expo.modules.sharing:13.1.5"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/a9bfde12bc44e3e6412c81c9908b63f2/transformed/expo.modules.sharing-13.1.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-v4:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/5fde982e3dd990d3004e359630a81b79/transformed/legacy-support-v4-1.0.0/jars/classes.jar"
      resolved="androidx.legacy:legacy-support-v4:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/5fde982e3dd990d3004e359630a81b79/transformed/legacy-support-v4-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.securestore:14.2.4@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/4d363a7f2bcc15fba48ffee0ec1cc12f/transformed/expo.modules.securestore-14.2.4/jars/classes.jar"
      resolved="host.exp.exponent:expo.modules.securestore:14.2.4"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/4d363a7f2bcc15fba48ffee0ec1cc12f/transformed/expo.modules.securestore-14.2.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.biometric:biometric:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/88b9f6ad7ed87d7d30486cdd2fcbb5f7/transformed/biometric-1.1.0/jars/classes.jar"
      resolved="androidx.biometric:biometric:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/88b9f6ad7ed87d7d30486cdd2fcbb5f7/transformed/biometric-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.6.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/22d97c11c7e69f31bac4e14a1df1d05a/transformed/fragment-1.6.1/jars/classes.jar"
      resolved="androidx.fragment:fragment:1.6.1"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/22d97c11c7e69f31bac4e14a1df1d05a/transformed/fragment-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.10.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/2d07a07a91737a64f88fc62e4e6e3c87/transformed/activity-1.10.0/jars/classes.jar"
      resolved="androidx.activity:activity:1.10.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/2d07a07a91737a64f88fc62e4e6e3c87/transformed/activity-1.10.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-ui:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/2ecacc9a77cadee2dd15a52b568a98f1/transformed/legacy-support-core-ui-1.0.0/jars/classes.jar"
      resolved="androidx.legacy:legacy-support-core-ui:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/2ecacc9a77cadee2dd15a52b568a98f1/transformed/legacy-support-core-ui-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/c3179e0f1b67cb33410fa674edb5496c/transformed/swiperefreshlayout-1.1.0/jars/classes.jar"
      resolved="androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/c3179e0f1b67cb33410fa674edb5496c/transformed/swiperefreshlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/fcba58dc1b88ff592a0c160f99cb9936/transformed/legacy-support-core-utils-1.0.0/jars/classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/fcba58dc1b88ff592a0c160f99cb9936/transformed/legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/34564e277d995fd6c7ae4ce24f36e074/transformed/loader-1.0.0/jars/classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/34564e277d995fd6c7ae4ce24f36e074/transformed/loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/fa08d204398219a1a89b242b31fa3041/transformed/lifecycle-livedata-2.6.2/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.6.2"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/fa08d204398219a1a89b242b31fa3041/transformed/lifecycle-livedata-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/8fbfdc943d04e82c10add9c26833a5c6/transformed/lifecycle-livedata-core-2.6.2/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.6.2"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/8fbfdc943d04e82c10add9c26833a5c6/transformed/lifecycle-livedata-core-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/44911df0b7c9625b5575bd1446144add/transformed/lifecycle-viewmodel-2.6.2/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.6.2"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/44911df0b7c9625b5575bd1446144add/transformed/lifecycle-viewmodel-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/c4b770dc8989251f37e56402d8aa1685/transformed/lifecycle-viewmodel-savedstate-2.6.2/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/c4b770dc8989251f37e56402d8aa1685/transformed/lifecycle-viewmodel-savedstate-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/4bae3fed21d5f02d6bf43c03532e29f6/transformed/core-ktx-1.13.1/jars/classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/4bae3fed21d5f02d6bf43c03532e29f6/transformed/core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/34bb67911f8414f1ff69475eb4175853/transformed/drawerlayout-1.1.1/jars/classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/34bb67911f8414f1ff69475eb4175853/transformed/drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/8b13948c497b3cb25b633e3d026cff9f/transformed/vectordrawable-animated-1.1.0/jars/classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/8b13948c497b3cb25b633e3d026cff9f/transformed/vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/69082f68bb02655e2ffff4dadfa40175/transformed/vectordrawable-1.1.0/jars/classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/69082f68bb02655e2ffff4dadfa40175/transformed/vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/6183b82bc0c6a1834671e2c74c065f3b/transformed/media-1.0.0/jars/classes.jar"
      resolved="androidx.media:media:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/6183b82bc0c6a1834671e2c74c065f3b/transformed/media-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/ce7fe74a7e1d15baec12eb1258cf58fd/transformed/viewpager-1.0.0/jars/classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/ce7fe74a7e1d15baec12eb1258cf58fd/transformed/viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/94f09ae075da36273dd5b0f48955e034/transformed/coordinatorlayout-1.2.0/jars/classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.2.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/94f09ae075da36273dd5b0f48955e034/transformed/coordinatorlayout-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.slidingpanelayout:slidingpanelayout:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/7776c4e06fd334733f4fe348ed5c13e9/transformed/slidingpanelayout-1.0.0/jars/classes.jar"
      resolved="androidx.slidingpanelayout:slidingpanelayout:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/7776c4e06fd334733f4fe348ed5c13e9/transformed/slidingpanelayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/625e222979d00247a55c8d8eb69453bf/transformed/customview-1.1.0/jars/classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/625e222979d00247a55c8d8eb69453bf/transformed/customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/b4c4dee6b60fa7565c562d06ae78c19e/transformed/asynclayoutinflater-1.0.0/jars/classes.jar"
      resolved="androidx.asynclayoutinflater:asynclayoutinflater:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/b4c4dee6b60fa7565c562d06ae78c19e/transformed/asynclayoutinflater-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1/jars/classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/6c6e309b72c7c7f21fbfce9b95a8faf6/transformed/core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/5379c5771c0cc88a961c78af9bb09339/transformed/lifecycle-runtime-2.6.2/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.6.2"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/5379c5771c0cc88a961c78af9bb09339/transformed/lifecycle-runtime-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common/2.6.2/10f354fdb64868baecd67128560c5a0d6312c495/lifecycle-common-2.6.2.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.6.2"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-android/1.8.1/73e2acdd18df99dd4849d99f188dff529fc0afe0/kotlinx-coroutines-android-1.8.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/19f8795450958262f34e41d8151462d1/transformed/savedstate-1.2.1/jars/classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/19f8795450958262f34e41d8151462d1/transformed/savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:fbcore:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/971b53c391360c5dc5f7c648986b09b6/transformed/fbcore-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:fbcore:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/971b53c391360c5dc5f7c648986b09b6/transformed/fbcore-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:drawee:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/90554224eff49d6253bc7f46090a200f/transformed/drawee-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:drawee:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/90554224eff49d6253bc7f46090a200f/transformed/drawee-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk8/1.8.22/b25c86d47d6b962b9cf0f8c3f320c8a10eea3dd1/kotlin-stdlib-jdk8-1.8.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.22"/>
  <library
      name=":@@:expo-constants::release"
      project=":expo-constants"/>
  <library
      name=":@@:expo-image-manipulator::release"
      project=":expo-image-manipulator"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/0e6b28d36dd1dcea484117e191bbf338/transformed/cursoradapter-1.0.0/jars/classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/0e6b28d36dd1dcea484117e191bbf338/transformed/cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/16ea918983671f6172c82665df93c2bc/transformed/interpolator-1.0.0/jars/classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/16ea918983671f6172c82665df93c2bc/transformed/interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/67803a5cacc8f200daadaf81b44424d4/transformed/gifdecoder-4.16.0/jars/classes.jar"
      resolved="com.github.bumptech.glide:gifdecoder:4.16.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/67803a5cacc8f200daadaf81b44424d4/transformed/gifdecoder-4.16.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/c6aadd448bec4e14e12d110512d17b3c/transformed/versionedparcelable-1.1.1/jars/classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/c6aadd448bec4e14e12d110512d17b3c/transformed/versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.collection/collection/1.1.0/1f27220b47669781457de0d600849a5de0e89909/collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/63bd8e0afbc574e96db736c0dfd2bf6a/transformed/documentfile-1.0.0/jars/classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/63bd8e0afbc574e96db736c0dfd2bf6a/transformed/documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/839cc9c259c516d7f6f4a5b066ac62ea/transformed/localbroadcastmanager-1.0.0/jars/classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/839cc9c259c516d7f6f4a5b066ac62ea/transformed/localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/497868229bdb3c0c11614fe7fa96dcc3/transformed/print-1.0.0/jars/classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/497868229bdb3c0c11614fe7fa96dcc3/transformed/print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/3b4cadc5d581274a383a17f34b96bacf/transformed/core-runtime-2.2.0/jars/classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/3b4cadc5d581274a383a17f34b96bacf/transformed/core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-common/2.2.0/5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3/core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.8.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.annotation/annotation-jvm/1.8.1/b8a16fe526014b7941c1debaccaf9c5153692dbb/annotation-jvm-1.8.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.8.1"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/2d443ea4419c363d5cc1fea0dc777e76/transformed/annotation-experimental-1.4.0/jars/classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/2d443ea4419c363d5cc1fea0dc777e76/transformed/annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:ui-core:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/e69ea34ae74c1f0868245de2a10f55c0/transformed/ui-core-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:ui-core:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/e69ea34ae74c1f0868245de2a10f55c0/transformed/ui-core-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/22dca7957a69d0d46a2b301d1f3405bd/transformed/imagepipeline-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:imagepipeline:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/22dca7957a69d0d46a2b301d1f3405bd/transformed/imagepipeline-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:imagepipeline-base:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/7e8ff34aa003f1e3d34ab42e7a02199e/transformed/imagepipeline-base-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:imagepipeline-base:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/7e8ff34aa003f1e3d34ab42e7a02199e/transformed/imagepipeline-base-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk7/2.0.21/222b2be42672d47c002c1b22ac9f030d781fc5db/kotlin-stdlib-jdk7-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:2.0.21"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/2.0.21/618b539767b4899b4660a83006e052b63f1db551/kotlin-stdlib-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.21"/>
  <library
      name=":@@:react-native-gesture-handler::release"
      project=":react-native-gesture-handler"/>
  <library
      name=":@@:react-native-reanimated::release"
      project=":react-native-reanimated"/>
  <library
      name=":@@:react-native-svg::release"
      project=":react-native-svg"/>
  <library
      name=":@@:react-native-safe-area-context::release"
      project=":react-native-safe-area-context"/>
  <library
      name=":@@:react-native-async-storage_async-storage::release"
      project=":react-native-async-storage_async-storage"/>
  <library
      name="com.facebook.fresco:animated-gif:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/0fd0e4fa611fd75b8ba4eda3c0af56e5/transformed/animated-gif-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:animated-gif:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/0fd0e4fa611fd75b8ba4eda3c0af56e5/transformed/animated-gif-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:webpsupport:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/76328da2fdf1aca21629387770b9d62c/transformed/webpsupport-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:webpsupport:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/76328da2fdf1aca21629387770b9d62c/transformed/webpsupport-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.react:hermes-android:0.79.5:release@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/41bff831db8378bc4365744b809d6eab/transformed/hermes-android-0.79.5-release/jars/classes.jar"
      resolved="com.facebook.react:hermes-android:0.79.5"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/41bff831db8378bc4365744b809d6eab/transformed/hermes-android-0.79.5-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains/annotations/23.0.0/8cc20c07506ec18e0834947b84a864bfc094484e/annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="commons-io:commons-io:2.6@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/commons-io/commons-io/2.6/815893df5f31da2ece4040fe0a12fd44b577afaf/commons-io-2.6.jar"
      resolved="commons-io:commons-io:2.6"/>
  <library
      name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.github.bumptech.glide/disklrucache/4.16.0/411aa175d50d10b37c7a1a04d21a4e7145249557/disklrucache-4.16.0.jar"
      resolved="com.github.bumptech.glide:disklrucache:4.16.0"/>
  <library
      name="com.github.bumptech.glide:annotations:4.16.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.github.bumptech.glide/annotations/4.16.0/90730f6498299d207aa0878124ab7585969808f0/annotations-4.16.0.jar"
      resolved="com.github.bumptech.glide:annotations:4.16.0"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/5e0f9e6bf46b1bd6aeea3c4a7ba42299/transformed/tracing-1.2.0/jars/classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/5e0f9e6bf46b1bd6aeea3c4a7ba42299/transformed/tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.medialibrary:17.1.7@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/a2cbb89982dd856421bd2da7e11e062a/transformed/expo.modules.medialibrary-17.1.7/jars/classes.jar"
      resolved="host.exp.exponent:expo.modules.medialibrary:17.1.7"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/a2cbb89982dd856421bd2da7e11e062a/transformed/expo.modules.medialibrary-17.1.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.7@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/c63e2c30804d4489dd097529299f3a35/transformed/exifinterface-1.3.7/jars/classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.7"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/c63e2c30804d4489dd097529299f3a35/transformed/exifinterface-1.3.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/50fb524026069718687c4ab652c29053/transformed/autofill-1.1.0/jars/classes.jar"
      resolved="androidx.autofill:autofill:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/50fb524026069718687c4ab652c29053/transformed/autofill-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fbjni:fbjni:0.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/jars/classes.jar"
      resolved="com.facebook.fbjni:fbjni:0.7.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:soloader:0.12.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1/jars/classes.jar"
      resolved="com.facebook.soloader:soloader:0.12.1"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/b5e6c600671b7746d7499dc4b2839001/transformed/soloader-0.12.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.soloader:nativeloader:0.12.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.facebook.soloader/nativeloader/0.12.1/492cc5082540e19b29328f2f56c53255cb6e7cc6/nativeloader-0.12.1.jar"
      resolved="com.facebook.soloader:nativeloader:0.12.1"/>
  <library
      name="com.facebook.soloader:annotation:0.12.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.facebook.soloader/annotation/0.12.1/945ada76f62253ba8e72cbf755d0e85ea7362cfe/annotation-0.12.1.jar"
      resolved="com.facebook.soloader:annotation:0.12.1"/>
  <library
      name="com.facebook.infer.annotation:infer-annotation:0.18.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.facebook.infer.annotation/infer-annotation/0.18.0/27539793fe93ed7d92b6376281c16cda8278ab2f/infer-annotation-0.18.0.jar"
      resolved="com.facebook.infer.annotation:infer-annotation:0.18.0"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.code.findbugs/jsr305/3.0.2/25ea2e8b0c338a877313bd4672d3fe056ea78f0d/jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-annotations-jvm/1.3.72/7dba6c57de526588d8080317bda0c14cd88c8055/kotlin-annotations-jvm-1.3.72.jar"
      resolved="org.jetbrains.kotlin:kotlin-annotations-jvm:1.3.72"/>
  <library
      name="com.facebook.fresco:imagepipeline-native:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/433663fad8d0a369f9eebadfaf8d9c89/transformed/imagepipeline-native-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:imagepipeline-native:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/433663fad8d0a369f9eebadfaf8d9c89/transformed/imagepipeline-native-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-ashmem:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/3c5f6b0c155786f68981012d876a0286/transformed/memory-type-ashmem-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:memory-type-ashmem:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/3c5f6b0c155786f68981012d876a0286/transformed/memory-type-ashmem-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-native:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/33b3f812d0a9954143308667fe9e3f2b/transformed/memory-type-native-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:memory-type-native:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/33b3f812d0a9954143308667fe9e3f2b/transformed/memory-type-native-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:memory-type-java:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/a7b8bc68d236b8b6e7876128119c56df/transformed/memory-type-java-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:memory-type-java:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/a7b8bc68d236b8b6e7876128119c56df/transformed/memory-type-java-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagefilters:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/27d6ba0419c23ccfd28f0d65dba268b6/transformed/nativeimagefilters-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:nativeimagefilters:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/27d6ba0419c23ccfd28f0d65dba268b6/transformed/nativeimagefilters-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:nativeimagetranscoder:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/d87c01d0ffee9e478b5ed3aedb0a67be/transformed/nativeimagetranscoder-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:nativeimagetranscoder:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/d87c01d0ffee9e478b5ed3aedb0a67be/transformed/nativeimagetranscoder-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.yoga:proguard-annotations:1.19.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.facebook.yoga/proguard-annotations/1.19.0/fcbbb39052e6490eaaf6a6959c49c3a4fbe87c63/proguard-annotations-1.19.0.jar"
      resolved="com.facebook.yoga:proguard-annotations:1.19.0"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/javax.inject/javax.inject/1/6975da39a7040257bd51d21a231b76c915872d38/javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="expo.modules.asset:expo.modules.asset:11.1.7@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/bdeef16b1312dc3dd41940111e1a6fb3/transformed/expo.modules.asset-11.1.7/jars/classes.jar"
      resolved="expo.modules.asset:expo.modules.asset:11.1.7"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/bdeef16b1312dc3dd41940111e1a6fb3/transformed/expo.modules.asset-11.1.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="commons-codec:commons-codec:1.10@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/commons-codec/commons-codec/1.10/4b95f4897fa13f2cd904aee711aeafc0c5295cd8/commons-codec-1.10.jar"
      resolved="commons-codec:commons-codec:1.10"/>
  <library
      name="host.exp.exponent:expo.modules.font:13.3.2@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/c41c0a1db40479f33d891af06550e7a7/transformed/expo.modules.font-13.3.2/jars/classes.jar"
      resolved="host.exp.exponent:expo.modules.font:13.3.2"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/c41c0a1db40479f33d891af06550e7a7/transformed/expo.modules.font-13.3.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.imagepicker:16.1.4@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4/jars/classes.jar"
      resolved="host.exp.exponent:expo.modules.imagepicker:16.1.4"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/f59bc7b9bcb613678ec179424c84d627/transformed/expo.modules.imagepicker-16.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.keepawake:14.1.4@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/a28908b56ec11751b59243b1f242702a/transformed/expo.modules.keepawake-14.1.4/jars/classes.jar"
      resolved="host.exp.exponent:expo.modules.keepawake:14.1.4"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/a28908b56ec11751b59243b1f242702a/transformed/expo.modules.keepawake-14.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="host.exp.exponent:expo.modules.linking:7.1.7@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/478a7daaf5df250b58ae7be0b2bd8ba4/transformed/expo.modules.linking-7.1.7/jars/classes.jar"
      resolved="host.exp.exponent:expo.modules.linking:7.1.7"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/478a7daaf5df250b58ae7be0b2bd8ba4/transformed/expo.modules.linking-7.1.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.12.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0/jars/classes.jar"
      resolved="com.google.android.material:material:1.12.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/7becb12098085a58be85c10a694bf84d/transformed/material-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:animated-base:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/7f3cd27b890a8689bdecbf95693f6abf/transformed/animated-base-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:animated-base:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/7f3cd27b890a8689bdecbf95693f6abf/transformed/animated-base-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:animated-drawable:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/e843b317ee776dce67bc96b95fa8519e/transformed/animated-drawable-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:animated-drawable:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/e843b317ee776dce67bc96b95fa8519e/transformed/animated-drawable-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:vito-options:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/43cd96b77f258442464a756005d7068b/transformed/vito-options-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:vito-options:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/43cd96b77f258442464a756005d7068b/transformed/vito-options-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:urimod:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/42f63906eff2f4cf34b7774f8da390db/transformed/urimod-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:urimod:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/42f63906eff2f4cf34b7774f8da390db/transformed/urimod-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:vito-source:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/c02947a63af0bbe4d1d82d147698eaf8/transformed/vito-source-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:vito-source:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/c02947a63af0bbe4d1d82d147698eaf8/transformed/vito-source-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.vanniktech:android-image-cropper:4.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/22c05e568ac5d149f07f1e99d719b136/transformed/android-image-cropper-4.6.0/jars/classes.jar"
      resolved="com.vanniktech:android-image-cropper:4.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/22c05e568ac5d149f07f1e99d719b136/transformed/android-image-cropper-4.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.0.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/162c641a6ded6de9dcbbe71bb887fa9c/transformed/constraintlayout-2.0.1/jars/classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.0.1"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/162c641a6ded6de9dcbbe71bb887fa9c/transformed/constraintlayout-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/65ba2f9fa3c9fe83e5152e0f7195116c/transformed/viewpager2-1.0.0/jars/classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/65ba2f9fa3c9fe83e5152e0f7195116c/transformed/viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.6.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/506e255793c8bac2342ad449777733c1/transformed/fragment-ktx-1.6.1/jars/classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.6.1"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/506e255793c8bac2342ad449777733c1/transformed/fragment-ktx-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.facebook.fresco:soloader:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/ad8c48db4554b56c12f5e68dfae98b63/transformed/soloader-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:soloader:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/ad8c48db4554b56c12f5e68dfae98b63/transformed/soloader-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.5.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/925e579c481c06349ca2c1bdadbdec35/transformed/transition-1.5.0/jars/classes.jar"
      resolved="androidx.transition:transition:1.5.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/925e579c481c06349ca2c1bdadbdec35/transformed/transition-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/74502918765ad8f81a97aa9e7ecb2fe7/transformed/dynamicanimation-1.0.0/jars/classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/74502918765ad8f81a97aa9e7ecb2fe7/transformed/dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.3.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/ec1853d0b0976e32a4f952e316c0c604/transformed/emoji2-views-helper-1.3.0/jars/classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.3.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/ec1853d0b0976e32a4f952e316c0c604/transformed/emoji2-views-helper-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/jars/classes.jar:/Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0/jars/libs/repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/5895da00ec1caed18a5ab5e37ba29377/transformed/emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.1.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/2db4240268c509e9b190ca1a393ca7d5/transformed/recyclerview-1.1.0/jars/classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.1.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/2db4240268c509e9b190ca1a393ca7d5/transformed/recyclerview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.10.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/5d130b720d8f8e13290a97037e7ad612/transformed/activity-ktx-1.10.0/jars/classes.jar"
      resolved="androidx.activity:activity-ktx:1.10.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/5d130b720d8f8e13290a97037e7ad612/transformed/activity-ktx-1.10.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/6d8784d24425b9166c30f1a4f5571e15/transformed/savedstate-ktx-1.2.1/jars/classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/6d8784d24425b9166c30f1a4f5571e15/transformed/savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/98eec38dab2893f9dde1642e7a91ed70/transformed/lifecycle-viewmodel-ktx-2.6.2/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/98eec38dab2893f9dde1642e7a91ed70/transformed/lifecycle-viewmodel-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.6.2@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/2af3bcc447ac110862396e7d0b0ec9ae/transformed/lifecycle-runtime-ktx-2.6.2/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.6.2"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/2af3bcc447ac110862396e7d0b0ec9ae/transformed/lifecycle-runtime-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.6.2@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.6.2"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/a60d671dd3aa291ecde7f9adf86d9123/transformed/lifecycle-process-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/0e45932910c15ea66169daadac963681/transformed/lifecycle-livedata-core-ktx-2.6.2/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/0e45932910c15ea66169daadac963681/transformed/lifecycle-livedata-core-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-reflect:2.0.21@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-reflect/2.0.21/669e1d35e4ca1797f9ddb2830dd6c36c0ca531e4/kotlin-reflect-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-reflect:2.0.21"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.4.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0/jars/classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.4.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/402febaab5c38352dd0336b69f2bbad7/transformed/profileinstaller-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1/jars/classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/12f7ec3034c44ad7915028fd2e88ba60/transformed/startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing-ktx:1.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/05c532ea6eeed5ba96fff64378302997/transformed/tracing-ktx-1.2.0/jars/classes.jar"
      resolved="androidx.tracing:tracing-ktx:1.2.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/05c532ea6eeed5ba96fff64378302997/transformed/tracing-ktx-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.1.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.collection/collection-ktx/1.1.0/f807b2f366f7b75142a67d2f3c10031065b5168/collection-ktx-1.1.0.jar"
      resolved="androidx.collection:collection-ktx:1.1.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-parcelize-runtime/2.0.0/64302a285bd7779196c44a9ceae188ebd62ea67/kotlin-parcelize-runtime-2.0.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:2.0.0"/>
  <library
      name="com.facebook.fresco:vito-renderer:3.6.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/9f527975834136bd1e4e42d80a2ffd4f/transformed/vito-renderer-3.6.0/jars/classes.jar"
      resolved="com.facebook.fresco:vito-renderer:3.6.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/9f527975834136bd1e4e42d80a2ffd4f/transformed/vito-renderer-3.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-android-extensions-runtime/2.0.0/7d968ca8316eee591ae4dd46b9337b1b7793ec57/kotlin-android-extensions-runtime-2.0.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:2.0.0"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/5745f858b74ff0a2f8e17d4b26e19812/transformed/cardview-1.0.0/jars/classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/5745f858b74ff0a2f8e17d4b26e19812/transformed/cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.resourceinspection/resourceinspection-annotation/1.0.1/8c21f8ff5d96d5d52c948707f7e4d6ca6773feef/resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.databinding:viewbinding:8.5.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.13/transforms/9640d4324f67d36bcedebf66d27909e3/transformed/viewbinding-8.5.1/jars/classes.jar"
      resolved="androidx.databinding:viewbinding:8.5.1"
      folder="/Users/<USER>/.gradle/caches/8.13/transforms/9640d4324f67d36bcedebf66d27909e3/transformed/viewbinding-8.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.concurrent/concurrent-futures/1.1.0/50b7fb98350d5f42a4e49704b03278542293ba48/concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="com.parse.bolts:bolts-tasks:1.4.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.parse.bolts/bolts-tasks/1.4.0/d85884acf6810a3bbbecb587f239005cbc846dc4/bolts-tasks-1.4.0.jar"
      resolved="com.parse.bolts:bolts-tasks:1.4.0"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.15.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.errorprone/error_prone_annotations/2.15.0/38c8485a652f808c8c149150da4e5c2b0bd17f9a/error_prone_annotations-2.15.0.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.15.0"/>
  <library
      name="androidx.constraintlayout:constraintlayout-solver:2.0.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.constraintlayout/constraintlayout-solver/2.0.1/30988fe2d77f3fe3bf7551bb8a8b795fad7e7226/constraintlayout-solver-2.0.1.jar"
      resolved="androidx.constraintlayout:constraintlayout-solver:2.0.1"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/listenablefuture/1.0/c949a840a6acbc5268d088e47b04177bf90b3cad/listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
</libraries>
