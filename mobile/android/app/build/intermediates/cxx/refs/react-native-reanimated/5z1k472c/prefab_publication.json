{"installationFolder": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/intermediates/prefab_package/release/prefab", "gradlePath": ":react-native-reanimated", "packageInfo": {"packageName": "react-native-reanimated", "packageVersion": "3.17.5", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "reanimated", "moduleHeaders": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/prefab-headers/reanimated", "moduleExportLibraries": [], "abis": [{"abiName": "x86_64", "abiApi": 24, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/intermediates/cxx/RelWithDebInfo/494y1u82/obj/x86_64/libreanimated.so", "abiAndroidGradleBuildJsonFile": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/494y1u82/x86_64/android_gradle_build.json"}]}, {"moduleName": "worklets", "moduleHeaders": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/prefab-headers/worklets", "moduleExportLibraries": [], "abis": [{"abiName": "x86_64", "abiApi": 24, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/build/intermediates/cxx/RelWithDebInfo/494y1u82/obj/x86_64/libworklets.so", "abiAndroidGradleBuildJsonFile": "/Users/<USER>/go/src/app/image-editor/mobile/node_modules/react-native-reanimated/android/.cxx/RelWithDebInfo/494y1u82/x86_64/android_gradle_build.json"}]}]}}