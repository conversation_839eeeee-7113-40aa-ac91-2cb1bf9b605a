import * as Sharing from 'expo-sharing';
import * as MediaLibrary from 'expo-media-library';
import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';

/**
 * Saves an image to the device's photo library
 */
export const saveToPhotoLibrary = async (imageUri: string): Promise<boolean> => {
  try {
    // Request permissions
    const { status } = await MediaLibrary.requestPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Please grant photo library permissions to save images.',
        [{ text: 'OK' }]
      );
      return false;
    }

    // Save to photo library
    const asset = await MediaLibrary.createAssetAsync(imageUri);
    
    Alert.alert(
      'Success',
      'Image saved to your photo library!',
      [{ text: 'OK' }]
    );
    
    return true;
  } catch (error) {
    console.error('Error saving to photo library:', error);
    Alert.alert(
      'Error',
      'Failed to save image to photo library. Please try again.',
      [{ text: 'OK' }]
    );
    return false;
  }
};

/**
 * Shares an image using the native share sheet
 */
export const shareImage = async (imageUri: string, message?: string): Promise<boolean> => {
  try {
    // Check if sharing is available
    const isAvailable = await Sharing.isAvailableAsync();
    if (!isAvailable) {
      Alert.alert(
        'Sharing Not Available',
        'Sharing is not available on this device.',
        [{ text: 'OK' }]
      );
      return false;
    }

    // Share the image
    await Sharing.shareAsync(imageUri, {
      mimeType: 'image/jpeg',
      dialogTitle: message || 'Share your edited photo',
    });

    return true;
  } catch (error) {
    console.error('Error sharing image:', error);
    Alert.alert(
      'Error',
      'Failed to share image. Please try again.',
      [{ text: 'OK' }]
    );
    return false;
  }
};

/**
 * Exports an image with options for save and share
 */
export const exportImage = async (imageUri: string): Promise<void> => {
  try {
    Alert.alert(
      'Export Image',
      'Choose how you want to export your edited image:',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Save to Photos',
          onPress: () => saveToPhotoLibrary(imageUri),
        },
        {
          text: 'Share',
          onPress: () => shareImage(imageUri, 'Check out my edited photo from Pixshop!'),
        },
      ]
    );
  } catch (error) {
    console.error('Error in export dialog:', error);
    Alert.alert('Error', 'Failed to export image');
  }
};

/**
 * Creates a copy of an image in the app's document directory for sharing
 */
export const createShareableImage = async (imageUri: string): Promise<string> => {
  try {
    const timestamp = Date.now();
    const filename = `pixshop_export_${timestamp}.jpg`;
    const shareableUri = `${FileSystem.documentDirectory}${filename}`;

    // Copy the image to a shareable location
    await FileSystem.copyAsync({
      from: imageUri,
      to: shareableUri,
    });

    return shareableUri;
  } catch (error) {
    console.error('Error creating shareable image:', error);
    throw new Error('Failed to prepare image for sharing');
  }
};

/**
 * Cleans up exported images from the document directory
 */
export const cleanupExportedImages = async (): Promise<void> => {
  try {
    const documentDir = FileSystem.documentDirectory;
    if (!documentDir) return;

    const files = await FileSystem.readDirectoryAsync(documentDir);
    const exportedImages = files.filter(file => file.startsWith('pixshop_export_'));

    // Keep only the last 10 exported images
    if (exportedImages.length > 10) {
      const sortedFiles = exportedImages.sort().slice(0, -10);
      
      await Promise.all(
        sortedFiles.map(file => 
          FileSystem.deleteAsync(`${documentDir}${file}`, { idempotent: true })
        )
      );
    }
  } catch (error) {
    console.warn('Error cleaning up exported images:', error);
  }
};
