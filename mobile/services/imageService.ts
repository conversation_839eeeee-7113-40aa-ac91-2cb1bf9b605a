import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import { CropData } from '../types';

/**
 * Saves a data URL as a temporary file and returns the file URI
 */
export const saveDataUrlAsFile = async (dataUrl: string): Promise<string> => {
  try {
    // Extract base64 data from data URL
    const base64Data = dataUrl.split(',')[1];
    if (!base64Data) {
      throw new Error('Invalid data URL format');
    }

    // Generate a unique filename
    const timestamp = Date.now();
    const filename = `edited_image_${timestamp}.jpg`;
    const fileUri = `${FileSystem.cacheDirectory}${filename}`;

    // Write the base64 data to a file
    await FileSystem.writeAsStringAsync(fileUri, base64Data, {
      encoding: FileSystem.EncodingType.Base64,
    });

    return fileUri;
  } catch (error) {
    console.error('Error saving data URL as file:', error);
    throw new Error('Failed to save edited image');
  }
};

/**
 * Gets image dimensions from a URI
 */
export const getImageDimensions = async (imageUri: string): Promise<{ width: number; height: number }> => {
  try {
    const result = await ImageManipulator.manipulateAsync(imageUri, [], {
      format: ImageManipulator.SaveFormat.JPEG,
    });
    
    return {
      width: result.width,
      height: result.height,
    };
  } catch (error) {
    console.error('Error getting image dimensions:', error);
    throw new Error('Failed to get image dimensions');
  }
};

/**
 * Crops an image based on crop data
 */
export const cropImage = async (
  imageUri: string,
  cropData: CropData
): Promise<string> => {
  try {
    const result = await ImageManipulator.manipulateAsync(
      imageUri,
      [
        {
          crop: {
            originX: cropData.x,
            originY: cropData.y,
            width: cropData.width,
            height: cropData.height,
          },
        },
      ],
      {
        compress: 0.9,
        format: ImageManipulator.SaveFormat.JPEG,
      }
    );

    return result.uri;
  } catch (error) {
    console.error('Error cropping image:', error);
    throw new Error('Failed to crop image');
  }
};

/**
 * Resizes an image to fit within maximum dimensions while maintaining aspect ratio
 */
export const resizeImage = async (
  imageUri: string,
  maxWidth: number,
  maxHeight: number
): Promise<string> => {
  try {
    const dimensions = await getImageDimensions(imageUri);
    const { width, height } = dimensions;

    // Calculate new dimensions maintaining aspect ratio
    let newWidth = width;
    let newHeight = height;

    if (width > maxWidth || height > maxHeight) {
      const widthRatio = maxWidth / width;
      const heightRatio = maxHeight / height;
      const ratio = Math.min(widthRatio, heightRatio);

      newWidth = Math.round(width * ratio);
      newHeight = Math.round(height * ratio);
    }

    // Only resize if dimensions changed
    if (newWidth !== width || newHeight !== height) {
      const result = await ImageManipulator.manipulateAsync(
        imageUri,
        [{ resize: { width: newWidth, height: newHeight } }],
        {
          compress: 0.9,
          format: ImageManipulator.SaveFormat.JPEG,
        }
      );

      return result.uri;
    }

    return imageUri;
  } catch (error) {
    console.error('Error resizing image:', error);
    throw new Error('Failed to resize image');
  }
};

/**
 * Converts screen coordinates to image coordinates
 */
export const screenToImageCoordinates = (
  screenX: number,
  screenY: number,
  imageWidth: number,
  imageHeight: number,
  displayWidth: number,
  displayHeight: number
): { x: number; y: number } => {
  // Calculate the scale factor
  const scaleX = imageWidth / displayWidth;
  const scaleY = imageHeight / displayHeight;

  return {
    x: Math.round(screenX * scaleX),
    y: Math.round(screenY * scaleY),
  };
};

/**
 * Cleans up temporary image files
 */
export const cleanupTempFiles = async (uris: string[]): Promise<void> => {
  try {
    await Promise.all(
      uris.map(async (uri) => {
        if (uri.startsWith(FileSystem.cacheDirectory || '')) {
          await FileSystem.deleteAsync(uri, { idempotent: true });
        }
      })
    );
  } catch (error) {
    console.warn('Error cleaning up temp files:', error);
  }
};
