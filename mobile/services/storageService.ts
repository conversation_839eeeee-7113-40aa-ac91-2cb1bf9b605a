import * as SecureStore from 'expo-secure-store';
import AsyncStorage from '@react-native-async-storage/async-storage';

const KEYS = {
  API_KEY: 'gemini_api_key',
  USER_PREFERENCES: 'user_preferences',
  RECENT_EDITS: 'recent_edits',
};

/**
 * Securely stores the Gemini API key
 */
export const storeApiKey = async (apiKey: string): Promise<void> => {
  try {
    await SecureStore.setItemAsync(KEYS.API_KEY, apiKey);
  } catch (error) {
    console.error('Error storing API key:', error);
    throw new Error('Failed to store API key');
  }
};

/**
 * Retrieves the stored Gemini API key
 */
export const getApiKey = async (): Promise<string | null> => {
  try {
    return await SecureStore.getItemAsync(KEYS.API_KEY);
  } catch (error) {
    console.error('Error retrieving API key:', error);
    return null;
  }
};

/**
 * Removes the stored API key
 */
export const removeApiKey = async (): Promise<void> => {
  try {
    await SecureStore.deleteItemAsync(KEYS.API_KEY);
  } catch (error) {
    console.error('Error removing API key:', error);
  }
};

/**
 * Stores user preferences
 */
export const storeUserPreferences = async (preferences: Record<string, any>): Promise<void> => {
  try {
    await AsyncStorage.setItem(KEYS.USER_PREFERENCES, JSON.stringify(preferences));
  } catch (error) {
    console.error('Error storing user preferences:', error);
    throw new Error('Failed to store user preferences');
  }
};

/**
 * Retrieves user preferences
 */
export const getUserPreferences = async (): Promise<Record<string, any> | null> => {
  try {
    const preferences = await AsyncStorage.getItem(KEYS.USER_PREFERENCES);
    return preferences ? JSON.parse(preferences) : null;
  } catch (error) {
    console.error('Error retrieving user preferences:', error);
    return null;
  }
};

/**
 * Stores recent edit history metadata
 */
export const storeRecentEdits = async (edits: Array<{ timestamp: number; type: string; prompt?: string }>): Promise<void> => {
  try {
    // Keep only the last 50 edits
    const recentEdits = edits.slice(-50);
    await AsyncStorage.setItem(KEYS.RECENT_EDITS, JSON.stringify(recentEdits));
  } catch (error) {
    console.error('Error storing recent edits:', error);
  }
};

/**
 * Retrieves recent edit history
 */
export const getRecentEdits = async (): Promise<Array<{ timestamp: number; type: string; prompt?: string }>> => {
  try {
    const edits = await AsyncStorage.getItem(KEYS.RECENT_EDITS);
    return edits ? JSON.parse(edits) : [];
  } catch (error) {
    console.error('Error retrieving recent edits:', error);
    return [];
  }
};

/**
 * Clears all stored data
 */
export const clearAllData = async (): Promise<void> => {
  try {
    await Promise.all([
      SecureStore.deleteItemAsync(KEYS.API_KEY),
      AsyncStorage.removeItem(KEYS.USER_PREFERENCES),
      AsyncStorage.removeItem(KEYS.RECENT_EDITS),
    ]);
  } catch (error) {
    console.error('Error clearing all data:', error);
  }
};
