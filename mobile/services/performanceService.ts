import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';

/**
 * Performance monitoring and optimization utilities
 */

interface PerformanceMetrics {
  timestamp: number;
  operation: string;
  duration: number;
  success: boolean;
  error?: string;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private maxMetrics = 100; // Keep last 100 operations

  startTimer(operation: string): () => void {
    const startTime = Date.now();
    
    return (success: boolean = true, error?: string) => {
      const duration = Date.now() - startTime;
      
      this.addMetric({
        timestamp: startTime,
        operation,
        duration,
        success,
        error,
      });
      
      // Log slow operations
      if (duration > 5000) { // 5 seconds
        console.warn(`Slow operation detected: ${operation} took ${duration}ms`);
      }
    };
  }

  private addMetric(metric: PerformanceMetrics) {
    this.metrics.push(metric);
    
    // Keep only recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }
  }

  getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  getAverageTime(operation: string): number {
    const operationMetrics = this.metrics.filter(m => m.operation === operation && m.success);
    if (operationMetrics.length === 0) return 0;
    
    const totalTime = operationMetrics.reduce((sum, m) => sum + m.duration, 0);
    return totalTime / operationMetrics.length;
  }

  getFailureRate(operation: string): number {
    const operationMetrics = this.metrics.filter(m => m.operation === operation);
    if (operationMetrics.length === 0) return 0;
    
    const failures = operationMetrics.filter(m => !m.success).length;
    return failures / operationMetrics.length;
  }
}

export const performanceMonitor = new PerformanceMonitor();

/**
 * Memory management utilities
 */
export const memoryManager = {
  /**
   * Cleans up temporary files older than specified age
   */
  cleanupOldTempFiles: async (maxAgeHours: number = 24): Promise<void> => {
    try {
      const cacheDir = FileSystem.cacheDirectory;
      if (!cacheDir) return;

      const files = await FileSystem.readDirectoryAsync(cacheDir);
      const cutoffTime = Date.now() - (maxAgeHours * 60 * 60 * 1000);

      for (const file of files) {
        try {
          const filePath = `${cacheDir}${file}`;
          const info = await FileSystem.getInfoAsync(filePath);
          
          if (info.exists && info.modificationTime && info.modificationTime < cutoffTime) {
            await FileSystem.deleteAsync(filePath, { idempotent: true });
          }
        } catch (error) {
          console.warn(`Failed to clean up file ${file}:`, error);
        }
      }
    } catch (error) {
      console.warn('Failed to cleanup temp files:', error);
    }
  },

  /**
   * Gets cache directory size
   */
  getCacheSize: async (): Promise<number> => {
    try {
      const cacheDir = FileSystem.cacheDirectory;
      if (!cacheDir) return 0;

      const files = await FileSystem.readDirectoryAsync(cacheDir);
      let totalSize = 0;

      for (const file of files) {
        try {
          const filePath = `${cacheDir}${file}`;
          const info = await FileSystem.getInfoAsync(filePath);
          if (info.exists && info.size) {
            totalSize += info.size;
          }
        } catch (error) {
          // Ignore individual file errors
        }
      }

      return totalSize;
    } catch (error) {
      console.warn('Failed to get cache size:', error);
      return 0;
    }
  },

  /**
   * Clears all cache if it exceeds the limit
   */
  manageCacheSize: async (maxSizeMB: number = 100): Promise<void> => {
    try {
      const currentSize = await memoryManager.getCacheSize();
      const maxSizeBytes = maxSizeMB * 1024 * 1024;

      if (currentSize > maxSizeBytes) {
        console.log(`Cache size (${Math.round(currentSize / 1024 / 1024)}MB) exceeds limit (${maxSizeMB}MB), cleaning up...`);
        
        const cacheDir = FileSystem.cacheDirectory;
        if (cacheDir) {
          const files = await FileSystem.readDirectoryAsync(cacheDir);
          
          // Delete oldest files first
          const fileInfos = await Promise.all(
            files.map(async (file) => {
              try {
                const filePath = `${cacheDir}${file}`;
                const info = await FileSystem.getInfoAsync(filePath);
                return { file, path: filePath, modificationTime: info.modificationTime || 0 };
              } catch {
                return null;
              }
            })
          );

          const validFiles = fileInfos.filter(Boolean) as Array<{
            file: string;
            path: string;
            modificationTime: number;
          }>;

          validFiles.sort((a, b) => a.modificationTime - b.modificationTime);

          // Delete half of the files
          const filesToDelete = validFiles.slice(0, Math.floor(validFiles.length / 2));
          
          for (const fileInfo of filesToDelete) {
            try {
              await FileSystem.deleteAsync(fileInfo.path, { idempotent: true });
            } catch (error) {
              console.warn(`Failed to delete ${fileInfo.file}:`, error);
            }
          }
        }
      }
    } catch (error) {
      console.warn('Failed to manage cache size:', error);
    }
  },
};

/**
 * Error handling utilities
 */
export const errorHandler = {
  /**
   * Handles and reports errors with user-friendly messages
   */
  handleError: (error: unknown, context: string, showAlert: boolean = true): void => {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    
    console.error(`Error in ${context}:`, error);
    
    // Log to performance monitor
    const timer = performanceMonitor.startTimer(`error_${context}`);
    timer(false, errorMessage);

    if (showAlert) {
      const userFriendlyMessage = errorHandler.getUserFriendlyMessage(errorMessage, context);
      Alert.alert('Error', userFriendlyMessage);
    }
  },

  /**
   * Converts technical error messages to user-friendly ones
   */
  getUserFriendlyMessage: (error: string, context: string): string => {
    const lowerError = error.toLowerCase();
    
    if (lowerError.includes('network') || lowerError.includes('fetch')) {
      return 'Please check your internet connection and try again.';
    }
    
    if (lowerError.includes('permission')) {
      return 'Please grant the necessary permissions in your device settings.';
    }
    
    if (lowerError.includes('api key') || lowerError.includes('unauthorized')) {
      return 'There was an issue with the AI service. Please try again later.';
    }
    
    if (lowerError.includes('storage') || lowerError.includes('space')) {
      return 'Not enough storage space. Please free up some space and try again.';
    }
    
    if (context.includes('image')) {
      return 'There was an issue processing your image. Please try with a different image.';
    }
    
    return 'Something went wrong. Please try again.';
  },

  /**
   * Retry mechanism for failed operations
   */
  withRetry: async <T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> => {
    let lastError: unknown;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxRetries) {
          throw error;
        }
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }
    
    throw lastError;
  },
};
