import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors, Spacing, Typography, Layout } from '../constants';
import { AspectRatio } from '../types';
import { cropImage, getImageDimensions } from '../services/imageService';

interface CropPanelProps {
  imageUri: string;
  onEditComplete: (newImageUri: string) => void;
  onEditError: (error: string) => void;
}

interface AspectRatioOption {
  id: AspectRatio;
  label: string;
  ratio?: number;
}

const aspectRatioOptions: AspectRatioOption[] = [
  { id: 'freeform', label: 'Freeform' },
  { id: '1:1', label: '1:1', ratio: 1 },
  { id: '16:9', label: '16:9', ratio: 16/9 },
  { id: '4:5', label: '4:5', ratio: 4/5 },
  { id: '3:2', label: '3:2', ratio: 3/2 },
];

export const CropPanel: React.FC<CropPanelProps> = ({
  imageUri,
  onEditComplete,
  onEditError,
}) => {
  const [selectedRatio, setSelectedRatio] = useState<AspectRatio>('freeform');
  const [isProcessing, setIsProcessing] = useState(false);

  const handleCrop = async () => {
    try {
      setIsProcessing(true);
      
      // Get image dimensions
      const dimensions = await getImageDimensions(imageUri);
      const { width, height } = dimensions;
      
      let cropData = {
        x: 0,
        y: 0,
        width: width,
        height: height,
      };

      // Apply aspect ratio if not freeform
      if (selectedRatio !== 'freeform') {
        const option = aspectRatioOptions.find(opt => opt.id === selectedRatio);
        if (option?.ratio) {
          const targetRatio = option.ratio;
          const currentRatio = width / height;
          
          if (currentRatio > targetRatio) {
            // Image is wider than target ratio, crop width
            const newWidth = height * targetRatio;
            cropData = {
              x: (width - newWidth) / 2,
              y: 0,
              width: newWidth,
              height: height,
            };
          } else {
            // Image is taller than target ratio, crop height
            const newHeight = width / targetRatio;
            cropData = {
              x: 0,
              y: (height - newHeight) / 2,
              width: width,
              height: newHeight,
            };
          }
        }
      }

      const croppedImageUri = await cropImage(imageUri, cropData);
      onEditComplete(croppedImageUri);
    } catch (error) {
      console.error('Error cropping image:', error);
      onEditError(error instanceof Error ? error.message : 'Failed to crop image');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.panel}>
        <View style={styles.header}>
          <Text style={styles.title}>Crop</Text>
        </View>

        <View style={styles.ratioContainer}>
          <Text style={styles.sectionTitle}>Aspect Ratio</Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.ratioScrollContainer}
          >
            {aspectRatioOptions.map((option) => {
              const isSelected = selectedRatio === option.id;
              
              return (
                <TouchableOpacity
                  key={option.id}
                  style={[
                    styles.ratioButton,
                    isSelected && styles.ratioButtonSelected,
                  ]}
                  onPress={() => setSelectedRatio(option.id)}
                >
                  <Text
                    style={[
                      styles.ratioButtonText,
                      isSelected && styles.ratioButtonTextSelected,
                    ]}
                  >
                    {option.label}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </ScrollView>
        </View>

        <TouchableOpacity
          style={[
            styles.cropButton,
            isProcessing && styles.cropButtonDisabled,
          ]}
          onPress={handleCrop}
          disabled={isProcessing}
        >
          <Ionicons 
            name="crop" 
            size={20} 
            color={Colors.primary} 
          />
          <Text style={styles.cropButtonText}>
            {isProcessing ? 'Processing...' : 'Apply Crop'}
          </Text>
        </TouchableOpacity>

        <View style={styles.infoContainer}>
          <Text style={styles.infoText}>
            Select an aspect ratio and tap "Apply Crop" to crop your image.
          </Text>
          <Text style={styles.infoText}>
            Freeform maintains the original proportions.
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: Layout.toolbarHeight,
    left: 0,
    right: 0,
    maxHeight: Layout.panelMaxHeight,
  },
  panel: {
    backgroundColor: Colors.surface,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.surfaceVariant,
  },
  header: {
    marginBottom: Spacing.md,
  },
  title: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semibold,
    color: Colors.primary,
  },
  ratioContainer: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    fontSize: Typography.sizes.md,
    fontWeight: Typography.weights.medium,
    color: Colors.primary,
    marginBottom: Spacing.sm,
  },
  ratioScrollContainer: {
    paddingHorizontal: Spacing.xs,
    gap: Spacing.sm,
  },
  ratioButton: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: 20,
    backgroundColor: Colors.surfaceVariant,
    borderWidth: 1,
    borderColor: Colors.surfaceVariant,
  },
  ratioButtonSelected: {
    backgroundColor: Colors.accent,
    borderColor: Colors.accent,
  },
  ratioButtonText: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.medium,
    color: Colors.secondary,
  },
  ratioButtonTextSelected: {
    color: Colors.primary,
    fontWeight: Typography.weights.semibold,
  },
  cropButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.accent,
    borderRadius: 12,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
    gap: Spacing.sm,
  },
  cropButtonDisabled: {
    backgroundColor: Colors.surfaceVariant,
    opacity: 0.5,
  },
  cropButtonText: {
    fontSize: Typography.sizes.md,
    fontWeight: Typography.weights.semibold,
    color: Colors.primary,
  },
  infoContainer: {
    marginTop: Spacing.sm,
  },
  infoText: {
    fontSize: Typography.sizes.sm,
    color: Colors.secondary,
    marginBottom: Spacing.xs / 2,
    textAlign: 'center',
  },
});
