import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors, Spacing, Typography, Layout } from '../constants';
import { EditingTool } from '../types';

interface ToolbarProps {
  activeTool: EditingTool;
  onToolChange: (tool: EditingTool) => void;
}

interface ToolItem {
  id: EditingTool;
  label: string;
  icon: keyof typeof Ionicons.glyphMap;
}

const tools: ToolItem[] = [
  { id: 'retouch', label: 'Retouch', icon: 'brush-outline' },
  { id: 'crop', label: 'Crop', icon: 'crop-outline' },
  { id: 'adjust', label: 'Adjust', icon: 'options-outline' },
  { id: 'filters', label: 'Filters', icon: 'color-filter-outline' },
];

export const Toolbar: React.FC<ToolbarProps> = ({
  activeTool,
  onToolChange,
}) => {
  return (
    <View style={styles.container}>
      {tools.map((tool) => {
        const isActive = activeTool === tool.id;
        
        return (
          <TouchableOpacity
            key={tool.id}
            style={[styles.toolButton, isActive && styles.toolButtonActive]}
            onPress={() => onToolChange(tool.id)}
          >
            <Ionicons
              name={tool.icon}
              size={24}
              color={isActive ? Colors.accent : Colors.secondary}
            />
            <Text
              style={[
                styles.toolLabel,
                isActive && styles.toolLabelActive,
              ]}
            >
              {tool.label}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: Layout.toolbarHeight,
    flexDirection: 'row',
    backgroundColor: Colors.surface,
    borderTopWidth: 1,
    borderTopColor: Colors.surfaceVariant,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
  },
  toolButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.sm,
    borderRadius: 8,
  },
  toolButtonActive: {
    backgroundColor: Colors.surfaceVariant,
  },
  toolLabel: {
    fontSize: Typography.sizes.xs,
    color: Colors.secondary,
    marginTop: Spacing.xs,
    fontWeight: Typography.weights.medium,
  },
  toolLabelActive: {
    color: Colors.accent,
    fontWeight: Typography.weights.semibold,
  },
});
