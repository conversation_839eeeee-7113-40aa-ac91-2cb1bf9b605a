import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors, Spacing, Typography, Layout } from '../constants';

interface HeaderProps {
  onBack: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
  onExport: () => void;
  onReset: () => void;
  isLoading?: boolean;
}

export const Header: React.FC<HeaderProps> = ({
  onBack,
  onUndo,
  onRedo,
  onExport,
  onReset,
  isLoading = false,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.leftSection}>
        <TouchableOpacity style={styles.button} onPress={onBack}>
          <Ionicons name="arrow-back" size={24} color={Colors.primary} />
        </TouchableOpacity>
      </View>

      <View style={styles.centerSection}>
        <Text style={styles.title}>Pixshop</Text>
        {isLoading && (
          <ActivityIndicator 
            size="small" 
            color={Colors.accent} 
            style={styles.loadingIndicator}
          />
        )}
      </View>

      <View style={styles.rightSection}>
        <TouchableOpacity
          style={[styles.button, !onUndo && styles.buttonDisabled]}
          onPress={onUndo}
          disabled={!onUndo || isLoading}
        >
          <Ionicons 
            name="arrow-undo" 
            size={20} 
            color={onUndo && !isLoading ? Colors.primary : Colors.secondary} 
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, !onRedo && styles.buttonDisabled]}
          onPress={onRedo}
          disabled={!onRedo || isLoading}
        >
          <Ionicons 
            name="arrow-redo" 
            size={20} 
            color={onRedo && !isLoading ? Colors.primary : Colors.secondary} 
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.button}
          onPress={onReset}
          disabled={isLoading}
        >
          <Ionicons 
            name="refresh" 
            size={20} 
            color={isLoading ? Colors.secondary : Colors.primary} 
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.button}
          onPress={onExport}
          disabled={isLoading}
        >
          <Ionicons 
            name="share-outline" 
            size={20} 
            color={isLoading ? Colors.secondary : Colors.primary} 
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: Layout.headerHeight,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.md,
    backgroundColor: Colors.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.surfaceVariant,
  },
  leftSection: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },
  centerSection: {
    flex: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  rightSection: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: Spacing.xs,
  },
  button: {
    padding: Spacing.sm,
    borderRadius: 8,
  },
  buttonDisabled: {
    opacity: 0.5,
  },
  title: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.bold,
    color: Colors.primary,
  },
  loadingIndicator: {
    marginLeft: Spacing.sm,
  },
});
