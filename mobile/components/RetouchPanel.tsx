import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, KeyboardAvoidingView, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors, Spacing, Typography, Layout } from '../constants';
import { generateEditedImage } from '../services/geminiService';
import { saveDataUrlAsFile } from '../services/imageService';

interface RetouchPanelProps {
  imageUri: string;
  hotspot: { x: number; y: number } | null;
  onEditStart: () => void;
  onEditComplete: (newImageUri: string) => void;
  onEditError: (error: string) => void;
  onClearHotspot: () => void;
}

export const RetouchPanel: React.FC<RetouchPanelProps> = ({
  imageUri,
  hotspot,
  onEditStart,
  onEditComplete,
  onEditError,
  onClearHotspot,
}) => {
  const [prompt, setPrompt] = useState('');

  const handleGenerate = async () => {
    if (!hotspot || !prompt.trim()) {
      return;
    }

    try {
      onEditStart();
      
      const dataUrl = await generateEditedImage(imageUri, prompt.trim(), hotspot);
      const newImageUri = await saveDataUrlAsFile(dataUrl);
      
      onEditComplete(newImageUri);
      setPrompt('');
    } catch (error) {
      console.error('Error generating edited image:', error);
      onEditError(error instanceof Error ? error.message : 'Failed to generate edited image');
    }
  };

  const handleClear = () => {
    setPrompt('');
    onClearHotspot();
  };

  if (!hotspot) {
    return (
      <View style={styles.instructionContainer}>
        <Ionicons name="finger-print-outline" size={48} color={Colors.secondary} />
        <Text style={styles.instructionTitle}>Tap to Retouch</Text>
        <Text style={styles.instructionText}>
          Tap anywhere on the image to place a hotspot, then describe what you want to change.
        </Text>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <View style={styles.panel}>
        <View style={styles.header}>
          <Text style={styles.title}>Retouch</Text>
          <TouchableOpacity onPress={handleClear} style={styles.clearButton}>
            <Ionicons name="close" size={20} color={Colors.secondary} />
          </TouchableOpacity>
        </View>

        <View style={styles.inputContainer}>
          <TextInput
            style={styles.textInput}
            placeholder="Describe what you want to change..."
            placeholderTextColor={Colors.secondary}
            value={prompt}
            onChangeText={setPrompt}
            multiline
            maxLength={200}
            returnKeyType="done"
            blurOnSubmit
          />
          <Text style={styles.characterCount}>{prompt.length}/200</Text>
        </View>

        <TouchableOpacity
          style={[
            styles.generateButton,
            (!prompt.trim() || !hotspot) && styles.generateButtonDisabled,
          ]}
          onPress={handleGenerate}
          disabled={!prompt.trim() || !hotspot}
        >
          <Ionicons name="sparkles" size={20} color={Colors.primary} />
          <Text style={styles.generateButtonText}>Generate</Text>
        </TouchableOpacity>

        <View style={styles.exampleContainer}>
          <Text style={styles.exampleTitle}>Examples:</Text>
          <Text style={styles.exampleText}>• "Remove the blemish"</Text>
          <Text style={styles.exampleText}>• "Change shirt color to blue"</Text>
          <Text style={styles.exampleText}>• "Smooth the wrinkles"</Text>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: Layout.toolbarHeight,
    left: 0,
    right: 0,
    maxHeight: Layout.panelMaxHeight,
  },
  panel: {
    backgroundColor: Colors.surface,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.surfaceVariant,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  title: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semibold,
    color: Colors.primary,
  },
  clearButton: {
    padding: Spacing.xs,
  },
  inputContainer: {
    marginBottom: Spacing.md,
  },
  textInput: {
    backgroundColor: Colors.surfaceVariant,
    borderRadius: 12,
    padding: Spacing.md,
    fontSize: Typography.sizes.md,
    color: Colors.primary,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  characterCount: {
    fontSize: Typography.sizes.xs,
    color: Colors.secondary,
    textAlign: 'right',
    marginTop: Spacing.xs,
  },
  generateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.accent,
    borderRadius: 12,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
    gap: Spacing.sm,
  },
  generateButtonDisabled: {
    backgroundColor: Colors.surfaceVariant,
    opacity: 0.5,
  },
  generateButtonText: {
    fontSize: Typography.sizes.md,
    fontWeight: Typography.weights.semibold,
    color: Colors.primary,
  },
  exampleContainer: {
    marginTop: Spacing.sm,
  },
  exampleTitle: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.medium,
    color: Colors.primary,
    marginBottom: Spacing.xs,
  },
  exampleText: {
    fontSize: Typography.sizes.sm,
    color: Colors.secondary,
    marginBottom: Spacing.xs / 2,
  },
  instructionContainer: {
    position: 'absolute',
    bottom: Layout.toolbarHeight + Spacing.lg,
    left: Spacing.lg,
    right: Spacing.lg,
    backgroundColor: Colors.surface,
    borderRadius: 16,
    padding: Spacing.xl,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.surfaceVariant,
  },
  instructionTitle: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semibold,
    color: Colors.primary,
    marginTop: Spacing.md,
    marginBottom: Spacing.sm,
  },
  instructionText: {
    fontSize: Typography.sizes.md,
    color: Colors.secondary,
    textAlign: 'center',
    lineHeight: 20,
  },
});
