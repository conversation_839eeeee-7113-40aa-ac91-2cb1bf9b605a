import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors, Spacing, Typography, Layout } from '../constants';
import { FILTER_PRESETS } from '../types';
import { generateFilteredImage } from '../services/geminiService';
import { saveDataUrlAsFile } from '../services/imageService';

interface FilterPanelProps {
  imageUri: string;
  onEditStart: () => void;
  onEditComplete: (newImageUri: string) => void;
  onEditError: (error: string) => void;
}

export const FilterPanel: React.FC<FilterPanelProps> = ({
  imageUri,
  onEditStart,
  onEditComplete,
  onEditError,
}) => {
  const [customPrompt, setCustomPrompt] = useState('');
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null);

  const handlePresetSelect = (presetId: string) => {
    setSelectedPreset(presetId);
    setCustomPrompt('');
  };

  const handleGenerate = async () => {
    let prompt = '';
    
    if (selectedPreset) {
      const preset = FILTER_PRESETS.find(p => p.id === selectedPreset);
      if (preset) {
        prompt = preset.prompt;
      }
    } else if (customPrompt.trim()) {
      prompt = customPrompt.trim();
    }

    if (!prompt) {
      return;
    }

    try {
      onEditStart();
      
      const dataUrl = await generateFilteredImage(imageUri, prompt);
      const newImageUri = await saveDataUrlAsFile(dataUrl);
      
      onEditComplete(newImageUri);
      setCustomPrompt('');
      setSelectedPreset(null);
    } catch (error) {
      console.error('Error generating filtered image:', error);
      onEditError(error instanceof Error ? error.message : 'Failed to generate filtered image');
    }
  };

  const handleClear = () => {
    setCustomPrompt('');
    setSelectedPreset(null);
  };

  const canGenerate = selectedPreset || customPrompt.trim();

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <View style={styles.panel}>
        <View style={styles.header}>
          <Text style={styles.title}>Filters</Text>
          <TouchableOpacity onPress={handleClear} style={styles.clearButton}>
            <Ionicons name="refresh" size={20} color={Colors.secondary} />
          </TouchableOpacity>
        </View>

        <View style={styles.presetsContainer}>
          <Text style={styles.sectionTitle}>Style Filters</Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.presetsScrollContainer}
          >
            {FILTER_PRESETS.map((preset) => {
              const isSelected = selectedPreset === preset.id;
              
              return (
                <TouchableOpacity
                  key={preset.id}
                  style={[
                    styles.presetButton,
                    isSelected && styles.presetButtonSelected,
                  ]}
                  onPress={() => handlePresetSelect(preset.id)}
                >
                  <Text
                    style={[
                      styles.presetButtonText,
                      isSelected && styles.presetButtonTextSelected,
                    ]}
                  >
                    {preset.name}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </ScrollView>
        </View>

        <View style={styles.customContainer}>
          <Text style={styles.sectionTitle}>Custom Filter</Text>
          <TextInput
            style={[
              styles.textInput,
              selectedPreset && styles.textInputDisabled,
            ]}
            placeholder="Describe your filter style..."
            placeholderTextColor={Colors.secondary}
            value={customPrompt}
            onChangeText={(text) => {
              setCustomPrompt(text);
              if (text.trim()) {
                setSelectedPreset(null);
              }
            }}
            multiline
            maxLength={200}
            returnKeyType="done"
            blurOnSubmit
            editable={!selectedPreset}
          />
          <Text style={styles.characterCount}>{customPrompt.length}/200</Text>
        </View>

        <TouchableOpacity
          style={[
            styles.generateButton,
            !canGenerate && styles.generateButtonDisabled,
          ]}
          onPress={handleGenerate}
          disabled={!canGenerate}
        >
          <Ionicons name="color-filter" size={20} color={Colors.primary} />
          <Text style={styles.generateButtonText}>Apply Filter</Text>
        </TouchableOpacity>

        <View style={styles.exampleContainer}>
          <Text style={styles.exampleTitle}>Examples:</Text>
          <Text style={styles.exampleText}>• "80s retro aesthetic"</Text>
          <Text style={styles.exampleText}>• "Moody black and white"</Text>
          <Text style={styles.exampleText}>• "Warm sunset glow"</Text>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: Layout.toolbarHeight,
    left: 0,
    right: 0,
    maxHeight: Layout.panelMaxHeight,
  },
  panel: {
    backgroundColor: Colors.surface,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: Spacing.md,
    borderTopWidth: 1,
    borderTopColor: Colors.surfaceVariant,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  title: {
    fontSize: Typography.sizes.lg,
    fontWeight: Typography.weights.semibold,
    color: Colors.primary,
  },
  clearButton: {
    padding: Spacing.xs,
  },
  presetsContainer: {
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    fontSize: Typography.sizes.md,
    fontWeight: Typography.weights.medium,
    color: Colors.primary,
    marginBottom: Spacing.sm,
  },
  presetsScrollContainer: {
    paddingHorizontal: Spacing.xs,
    gap: Spacing.sm,
  },
  presetButton: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: 20,
    backgroundColor: Colors.surfaceVariant,
    borderWidth: 1,
    borderColor: Colors.surfaceVariant,
  },
  presetButtonSelected: {
    backgroundColor: Colors.accent,
    borderColor: Colors.accent,
  },
  presetButtonText: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.medium,
    color: Colors.secondary,
  },
  presetButtonTextSelected: {
    color: Colors.primary,
    fontWeight: Typography.weights.semibold,
  },
  customContainer: {
    marginBottom: Spacing.md,
  },
  textInput: {
    backgroundColor: Colors.surfaceVariant,
    borderRadius: 12,
    padding: Spacing.md,
    fontSize: Typography.sizes.md,
    color: Colors.primary,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  textInputDisabled: {
    opacity: 0.5,
  },
  characterCount: {
    fontSize: Typography.sizes.xs,
    color: Colors.secondary,
    textAlign: 'right',
    marginTop: Spacing.xs,
  },
  generateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.accent,
    borderRadius: 12,
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
    gap: Spacing.sm,
  },
  generateButtonDisabled: {
    backgroundColor: Colors.surfaceVariant,
    opacity: 0.5,
  },
  generateButtonText: {
    fontSize: Typography.sizes.md,
    fontWeight: Typography.weights.semibold,
    color: Colors.primary,
  },
  exampleContainer: {
    marginTop: Spacing.sm,
  },
  exampleTitle: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.medium,
    color: Colors.primary,
    marginBottom: Spacing.xs,
  },
  exampleText: {
    fontSize: Typography.sizes.sm,
    color: Colors.secondary,
    marginBottom: Spacing.xs / 2,
  },
});
