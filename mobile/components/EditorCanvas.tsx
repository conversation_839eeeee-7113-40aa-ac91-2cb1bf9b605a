import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Dimensions, ActivityIndicator } from 'react-native';
import { PinchGesture<PERSON><PERSON><PERSON>, PanGestureHandler, TapGestureHandler } from 'react-native-gesture-handler';
import Animated, { useSharedValue, useAnimatedStyle, useAnimatedGestureHandler, runOnJS } from 'react-native-reanimated';
import { Colors, Layout } from '../constants';
import { getImageDimensions, screenToImageCoordinates } from '../services/imageService';

interface EditorCanvasProps {
  imageUri: string;
  onImageTap?: (x: number, y: number) => void;
  hotspot?: { x: number; y: number } | null;
  isLoading?: boolean;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const canvasHeight = screenHeight - Layout.headerHeight - Layout.toolbarHeight;

export const EditorCanvas: React.FC<EditorCanvasProps> = ({
  imageUri,
  onImageTap,
  hotspot,
  isLoading = false,
}) => {
  const [imageDimensions, setImageDimensions] = useState<{ width: number; height: number } | null>(null);
  const [displayDimensions, setDisplayDimensions] = useState<{ width: number; height: number }>({ width: 0, height: 0 });

  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const focalX = useSharedValue(0);
  const focalY = useSharedValue(0);

  useEffect(() => {
    const loadImageDimensions = async () => {
      try {
        const dimensions = await getImageDimensions(imageUri);
        setImageDimensions(dimensions);
        
        // Calculate display dimensions to fit the canvas
        const aspectRatio = dimensions.width / dimensions.height;
        let displayWidth = screenWidth;
        let displayHeight = screenWidth / aspectRatio;
        
        if (displayHeight > canvasHeight) {
          displayHeight = canvasHeight;
          displayWidth = canvasHeight * aspectRatio;
        }
        
        setDisplayDimensions({ width: displayWidth, height: displayHeight });
      } catch (error) {
        console.error('Error loading image dimensions:', error);
      }
    };

    loadImageDimensions();
  }, [imageUri]);

  const handleTap = (x: number, y: number) => {
    if (onImageTap && imageDimensions) {
      // Convert screen coordinates to image coordinates
      const imageCoords = screenToImageCoordinates(
        x,
        y,
        imageDimensions.width,
        imageDimensions.height,
        displayDimensions.width,
        displayDimensions.height
      );
      onImageTap(imageCoords.x, imageCoords.y);
    }
  };

  const tapHandler = useAnimatedGestureHandler({
    onEnd: (event) => {
      if (onImageTap) {
        runOnJS(handleTap)(event.x, event.y);
      }
    },
  });

  const pinchHandler = useAnimatedGestureHandler({
    onStart: (event) => {
      focalX.value = event.focalX;
      focalY.value = event.focalY;
    },
    onActive: (event) => {
      const newScale = Math.min(Math.max(event.scale, 1), 3);
      scale.value = newScale;
      
      const deltaX = event.focalX - focalX.value;
      const deltaY = event.focalY - focalY.value;
      
      translateX.value += deltaX;
      translateY.value += deltaY;
      
      focalX.value = event.focalX;
      focalY.value = event.focalY;
    },
    onEnd: () => {
      if (scale.value < 1) {
        scale.value = 1;
        translateX.value = 0;
        translateY.value = 0;
      }
    },
  });

  const panHandler = useAnimatedGestureHandler({
    onActive: (event) => {
      if (scale.value > 1) {
        translateX.value = event.translationX;
        translateY.value = event.translationY;
      }
    },
  });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: scale.value },
      ],
    };
  });

  const getHotspotPosition = () => {
    if (!hotspot || !imageDimensions) return null;
    
    // Convert image coordinates to display coordinates
    const displayX = (hotspot.x / imageDimensions.width) * displayDimensions.width;
    const displayY = (hotspot.y / imageDimensions.height) * displayDimensions.height;
    
    return { x: displayX, y: displayY };
  };

  const hotspotPosition = getHotspotPosition();

  if (!imageDimensions) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.accent} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <PinchGestureHandler onGestureEvent={pinchHandler}>
        <Animated.View style={styles.gestureContainer}>
          <PanGestureHandler onGestureEvent={panHandler}>
            <Animated.View style={styles.gestureContainer}>
              <TapGestureHandler onGestureEvent={tapHandler}>
                <Animated.View style={styles.imageContainer}>
                  <Animated.Image
                    source={{ uri: imageUri }}
                    style={[
                      styles.image,
                      {
                        width: displayDimensions.width,
                        height: displayDimensions.height,
                      },
                      animatedStyle,
                    ]}
                    resizeMode="contain"
                  />
                  
                  {hotspotPosition && (
                    <View
                      style={[
                        styles.hotspot,
                        {
                          left: hotspotPosition.x - 10,
                          top: hotspotPosition.y - 10,
                        },
                      ]}
                    />
                  )}
                </Animated.View>
              </TapGestureHandler>
            </Animated.View>
          </PanGestureHandler>
        </Animated.View>
      </PinchGestureHandler>
      
      {isLoading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={Colors.accent} />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  gestureContainer: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    borderRadius: 8,
  },
  hotspot: {
    position: 'absolute',
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: Colors.accent,
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
