# Pixshop Mobile

AI-powered photo editor for iOS and Android built with React Native and Expo.

## Features

- **AI-Powered Retouching**: Tap anywhere on your image and describe what you want to change
- **Creative Filters**: Apply artistic styles with simple text prompts
- **Professional Adjustments**: Global image enhancements and corrections
- **Intuitive Cropping**: Standard crop tool with aspect ratio presets
- **Full Edit History**: Undo/redo functionality with complete edit tracking
- **Native Integration**: Camera access, photo library, and native sharing

## Prerequisites

- Node.js (v16 or later)
- Expo CLI (`npm install -g @expo/cli`)
- iOS Simulator (for iOS development) or Android Studio (for Android development)
- Google Gemini API key

## Setup

1. **Clone and install dependencies:**
   ```bash
   cd pixshop-mobile
   npm install
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env
   ```
   Edit `.env` and add your Gemini API key:
   ```
   EXPO_PUBLIC_GEMINI_API_KEY=your_actual_api_key_here
   ```

3. **Start the development server:**
   ```bash
   npx expo start
   ```

4. **Run on device/simulator:**
   - Press `i` for iOS Simulator
   - Press `a` for Android Emulator
   - Scan QR code with Expo Go app on your device

## Project Structure

```
pixshop-mobile/
├── app/                    # Expo Router screens
│   ├── _layout.tsx        # Root layout
│   ├── index.tsx          # Start screen
│   └── editor.tsx         # Main editor
├── components/            # UI components
│   ├── Header.tsx         # Top navigation
│   ├── EditorCanvas.tsx   # Image display with gestures
│   ├── Toolbar.tsx        # Bottom tool navigation
│   ├── RetouchPanel.tsx   # Retouch interface
│   ├── CropPanel.tsx      # Crop interface
│   ├── AdjustPanel.tsx    # Adjustments interface
│   └── FilterPanel.tsx    # Filters interface
├── services/              # Core services
│   ├── geminiService.ts   # AI API integration
│   ├── imageService.ts    # Image processing
│   ├── sharingService.ts  # Export and sharing
│   └── storageService.ts  # Local data storage
├── hooks/                 # Custom React hooks
│   ├── useImageHistory.ts # Undo/redo functionality
│   ├── useImagePicker.ts  # Camera/library access
│   └── useGestures.ts     # Touch gestures
├── types/                 # TypeScript definitions
├── constants/             # App constants
└── assets/               # Images and icons
```

## Key Technologies

- **React Native & Expo**: Cross-platform mobile development
- **TypeScript**: Type-safe development
- **Expo Router**: File-based navigation
- **React Native Gesture Handler**: Touch interactions
- **React Native Reanimated**: Smooth animations
- **Google Gemini API**: AI-powered image generation
- **Expo Image Picker**: Camera and photo library access
- **Expo Sharing**: Native sharing capabilities

## Development

- **Start development server**: `npx expo start`
- **Clear cache**: `npx expo start --clear`
- **Build for production**: `npx expo build`
- **Run tests**: `npm test`

## Permissions

The app requires the following permissions:
- **Camera**: To take new photos for editing
- **Photo Library**: To select existing images
- **Storage**: To save edited images

## API Configuration

The app uses the Google Gemini API for AI-powered image editing. You'll need to:

1. Get a Gemini API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Add it to your `.env` file as `EXPO_PUBLIC_GEMINI_API_KEY`
3. The app will automatically use this key for all AI operations

## Building for Production

1. **Configure app signing** (iOS/Android specific)
2. **Build the app**:
   ```bash
   npx expo build:ios
   npx expo build:android
   ```
3. **Submit to app stores** using Expo's submission service or manually

## License

Apache 2.0 License - see the LICENSE file for details.
