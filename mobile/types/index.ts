export interface ImageAsset {
  uri: string;
  width: number;
  height: number;
}

export interface Hotspot {
  x: number;
  y: number;
}

export interface CropData {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface EditRequest {
  imageUri: string;
  prompt: string;
  hotspot?: Hotspot;
}

export interface EditResponse {
  success: boolean;
  imageUri?: string;
  error?: string;
}

export type EditingTool = 'retouch' | 'crop' | 'adjust' | 'filters';

export type AspectRatio = 'freeform' | '1:1' | '16:9' | '4:5' | '3:2';

export interface FilterPreset {
  id: string;
  name: string;
  prompt: string;
}

export interface AdjustmentPreset {
  id: string;
  name: string;
  prompt: string;
}

export const FILTER_PRESETS: FilterPreset[] = [
  { id: 'synthwave', name: 'Synthwave', prompt: 'Apply a vibrant synthwave aesthetic with neon colors and retro-futuristic style' },
  { id: 'anime', name: 'Anime', prompt: 'Transform into anime/manga art style with bold colors and clean lines' },
  { id: 'vintage', name: 'Vintage', prompt: 'Apply vintage film look with warm tones and slight grain' },
  { id: 'noir', name: 'Noir', prompt: 'Create a dramatic black and white noir style with high contrast' },
  { id: 'cyberpunk', name: 'Cyberpunk', prompt: 'Apply cyberpunk aesthetic with neon lights and dark urban atmosphere' },
];

export const ADJUSTMENT_PRESETS: AdjustmentPreset[] = [
  { id: 'blur_background', name: 'Blur Background', prompt: 'Blur the background while keeping the main subject sharp and in focus' },
  { id: 'enhance_details', name: 'Enhance Details', prompt: 'Enhance image details and sharpness while maintaining natural look' },
  { id: 'warm_lighting', name: 'Warm Lighting', prompt: 'Add warm, golden hour lighting to create a cozy atmosphere' },
  { id: 'dramatic_lighting', name: 'Dramatic Lighting', prompt: 'Add dramatic studio lighting with strong shadows and highlights' },
  { id: 'color_pop', name: 'Color Pop', prompt: 'Enhance colors to make them more vibrant and eye-catching' },
];
