import { useState, useEffect, useCallback } from 'react';
import { cleanupTempFiles } from '../services/imageService';

interface UseImageHistoryReturn {
  currentImage: string;
  canUndo: boolean;
  canRedo: boolean;
  undo: () => void;
  redo: () => void;
  addToHistory: (imageUri: string) => void;
  reset: () => void;
  historyLength: number;
}

export const useImageHistory = (initialImageUri: string): UseImageHistoryReturn => {
  const [history, setHistory] = useState<string[]>([initialImageUri]);
  const [historyIndex, setHistoryIndex] = useState(0);

  const currentImage = history[historyIndex];
  const canUndo = historyIndex > 0;
  const canRedo = historyIndex < history.length - 1;

  const undo = useCallback(() => {
    if (canUndo) {
      setHistoryIndex(prev => prev - 1);
    }
  }, [canUndo]);

  const redo = useCallback(() => {
    if (canRedo) {
      setHistoryIndex(prev => prev + 1);
    }
  }, [canRedo]);

  const addToHistory = useCallback((imageUri: string) => {
    try {
      setHistory(prev => {
        // Remove any future history if we're not at the end
        const newHistory = prev.slice(0, historyIndex + 1);

        // Add the new image
        newHistory.push(imageUri);

        // Limit history to 20 items to prevent memory issues
        if (newHistory.length > 20) {
          const removedItems = newHistory.splice(0, newHistory.length - 20);
          // Clean up old temp files
          cleanupTempFiles(removedItems.filter(uri => uri !== initialImageUri)).catch(error => {
            console.warn('Failed to cleanup temp files:', error);
          });
        }

        return newHistory;
      });

      // Move to the new image
      setHistoryIndex(prev => {
        const newHistory = history.slice(0, prev + 1);
        return newHistory.length; // This will be the index of the newly added item
      });
    } catch (error) {
      console.error('Error adding to history:', error);
    }
  }, [historyIndex, history, initialImageUri]);

  const reset = useCallback(() => {
    // Clean up all temp files except the original
    const tempFiles = history.filter(uri => uri !== initialImageUri);
    cleanupTempFiles(tempFiles);
    
    // Reset to original state
    setHistory([initialImageUri]);
    setHistoryIndex(0);
  }, [history, initialImageUri]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      const tempFiles = history.filter(uri => uri !== initialImageUri);
      cleanupTempFiles(tempFiles);
    };
  }, [history, initialImageUri]);

  return {
    currentImage,
    canUndo,
    canRedo,
    undo,
    redo,
    addToHistory,
    reset,
    historyLength: history.length,
  };
};
