import { useSharedValue, useAnimatedStyle, useAnimatedGestureHandler, runOnJS } from 'react-native-reanimated';
import { PinchGestureHand<PERSON>, PanGestureHandler } from 'react-native-gesture-handler';

interface UseGesturesProps {
  onTap?: (x: number, y: number) => void;
  minScale?: number;
  maxScale?: number;
}

interface UseGesturesReturn {
  scale: any;
  translateX: any;
  translateY: any;
  animatedStyle: any;
  pinchHandler: any;
  panHandler: any;
  tapHandler: any;
  resetTransform: () => void;
}

export const useGestures = ({
  onTap,
  minScale = 1,
  maxScale = 3,
}: UseGesturesProps = {}): UseGesturesReturn => {
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const focalX = useSharedValue(0);
  const focalY = useSharedValue(0);

  const pinchHandler = useAnimatedGestureHandler({
    onStart: (event) => {
      focalX.value = event.focalX;
      focalY.value = event.focalY;
    },
    onActive: (event) => {
      const newScale = Math.min(Math.max(event.scale, minScale), maxScale);
      scale.value = newScale;
      
      // Adjust translation to keep the focal point in place
      const deltaX = event.focalX - focalX.value;
      const deltaY = event.focalY - focalY.value;
      
      translateX.value += deltaX;
      translateY.value += deltaY;
      
      focalX.value = event.focalX;
      focalY.value = event.focalY;
    },
    onEnd: () => {
      // Snap back to bounds if needed
      if (scale.value < minScale) {
        scale.value = minScale;
      }
      if (scale.value > maxScale) {
        scale.value = maxScale;
      }
    },
  });

  const panHandler = useAnimatedGestureHandler({
    onStart: () => {
      // Store initial values
    },
    onActive: (event) => {
      // Only allow panning when zoomed in
      if (scale.value > 1) {
        translateX.value = event.translationX;
        translateY.value = event.translationY;
      }
    },
    onEnd: () => {
      // Add boundary constraints here if needed
    },
  });

  const tapHandler = useAnimatedGestureHandler({
    onEnd: (event) => {
      if (onTap) {
        runOnJS(onTap)(event.x, event.y);
      }
    },
  });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: scale.value },
      ],
    };
  });

  const resetTransform = () => {
    'worklet';
    scale.value = 1;
    translateX.value = 0;
    translateY.value = 0;
  };

  return {
    scale,
    translateX,
    translateY,
    animatedStyle,
    pinchHandler,
    panHandler,
    tapHandler,
    resetTransform,
  };
};
