#!/usr/bin/env node

/**
 * Comprehensive testing script for Pixshop Mobile
 * Tests app functionality, performance, and error handling
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Pixshop Mobile - Comprehensive Test Suite\n');

// Test 1: Project Structure Validation
console.log('📁 Testing project structure...');
const requiredFiles = [
  'app/_layout.tsx',
  'app/index.tsx',
  'app/editor.tsx',
  'components/Header.tsx',
  'components/EditorCanvas.tsx',
  'components/Toolbar.tsx',
  'components/RetouchPanel.tsx',
  'components/CropPanel.tsx',
  'components/AdjustPanel.tsx',
  'components/FilterPanel.tsx',
  'services/geminiService.ts',
  'services/imageService.ts',
  'services/sharingService.ts',
  'services/storageService.ts',
  'services/performanceService.ts',
  'hooks/useImageHistory.ts',
  'hooks/useImagePicker.ts',
  'hooks/useGestures.ts',
  'types/index.ts',
  'constants/index.ts',
  'package.json',
  'app.json',
];

let missingFiles = [];
for (const file of requiredFiles) {
  if (!fs.existsSync(path.join(__dirname, '..', file))) {
    missingFiles.push(file);
  }
}

if (missingFiles.length === 0) {
  console.log('✅ All required files present');
} else {
  console.log('❌ Missing files:', missingFiles);
}

// Test 2: Package Dependencies
console.log('\n📦 Testing package dependencies...');
const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
const requiredDependencies = [
  '@google/genai',
  'expo',
  'expo-router',
  'expo-image-picker',
  'expo-image-manipulator',
  'expo-sharing',
  'expo-media-library',
  'expo-file-system',
  'expo-constants',
  'expo-secure-store',
  'react-native-gesture-handler',
  'react-native-reanimated',
  'react-native-svg',
  '@react-native-async-storage/async-storage',
];

let missingDeps = [];
for (const dep of requiredDependencies) {
  if (!packageJson.dependencies[dep]) {
    missingDeps.push(dep);
  }
}

if (missingDeps.length === 0) {
  console.log('✅ All required dependencies installed');
} else {
  console.log('❌ Missing dependencies:', missingDeps);
}

// Test 3: TypeScript Configuration
console.log('\n🔧 Testing TypeScript configuration...');
if (fs.existsSync(path.join(__dirname, '..', 'tsconfig.json'))) {
  console.log('✅ TypeScript configuration found');
} else {
  console.log('❌ TypeScript configuration missing');
}

// Test 4: App Configuration
console.log('\n⚙️ Testing app configuration...');
const appJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'app.json'), 'utf8'));

const requiredConfig = {
  'expo.name': 'Pixshop Mobile',
  'expo.slug': 'pixshop-mobile',
  'expo.userInterfaceStyle': 'dark',
  'expo.plugins': ['expo-router'],
};

let configIssues = [];
for (const [key, expectedValue] of Object.entries(requiredConfig)) {
  const keys = key.split('.');
  let value = appJson;
  for (const k of keys) {
    value = value?.[k];
  }
  
  if (key === 'expo.plugins') {
    if (!Array.isArray(value) || !value.includes(expectedValue[0])) {
      configIssues.push(`${key} should include ${expectedValue[0]}`);
    }
  } else if (value !== expectedValue) {
    configIssues.push(`${key} should be "${expectedValue}", got "${value}"`);
  }
}

if (configIssues.length === 0) {
  console.log('✅ App configuration is correct');
} else {
  console.log('❌ Configuration issues:', configIssues);
}

// Test 5: Environment Setup
console.log('\n🌍 Testing environment setup...');
if (fs.existsSync(path.join(__dirname, '..', '.env.example'))) {
  console.log('✅ Environment example file found');
} else {
  console.log('❌ Environment example file missing');
}

// Test 6: Code Quality Checks
console.log('\n🔍 Running basic code quality checks...');

// Check for common issues in TypeScript files
const tsFiles = [
  'services/geminiService.ts',
  'services/imageService.ts',
  'hooks/useImageHistory.ts',
];

let codeIssues = [];
for (const file of tsFiles) {
  const filePath = path.join(__dirname, '..', file);
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check for proper error handling
    if (!content.includes('try') && !content.includes('catch')) {
      codeIssues.push(`${file}: Missing error handling`);
    }
    
    // Check for TypeScript types
    if (!content.includes('interface') && !content.includes('type ')) {
      codeIssues.push(`${file}: Consider adding TypeScript interfaces/types`);
    }
  }
}

if (codeIssues.length === 0) {
  console.log('✅ Basic code quality checks passed');
} else {
  console.log('⚠️ Code quality suggestions:', codeIssues);
}

// Test 7: Performance Considerations
console.log('\n⚡ Testing performance considerations...');

const performanceChecks = [];

// Check if performance monitoring is implemented
const perfServicePath = path.join(__dirname, '..', 'services/performanceService.ts');
if (fs.existsSync(perfServicePath)) {
  console.log('✅ Performance monitoring service found');
} else {
  performanceChecks.push('Performance monitoring service missing');
}

// Check for image optimization
const imageServicePath = path.join(__dirname, '..', 'services/imageService.ts');
if (fs.existsSync(imageServicePath)) {
  const content = fs.readFileSync(imageServicePath, 'utf8');
  if (content.includes('compress') || content.includes('resize')) {
    console.log('✅ Image optimization implemented');
  } else {
    performanceChecks.push('Image optimization could be improved');
  }
}

if (performanceChecks.length > 0) {
  console.log('⚠️ Performance suggestions:', performanceChecks);
}

// Test Summary
console.log('\n📊 Test Summary');
console.log('================');

const totalTests = 7;
const passedTests = [
  missingFiles.length === 0,
  missingDeps.length === 0,
  fs.existsSync(path.join(__dirname, '..', 'tsconfig.json')),
  configIssues.length === 0,
  fs.existsSync(path.join(__dirname, '..', '.env.example')),
  codeIssues.length === 0,
  performanceChecks.length === 0,
].filter(Boolean).length;

console.log(`✅ Passed: ${passedTests}/${totalTests} tests`);

if (passedTests === totalTests) {
  console.log('\n🎉 All tests passed! Your Pixshop Mobile app is ready for testing.');
  console.log('\n📱 Next steps:');
  console.log('1. Set up your .env file with EXPO_PUBLIC_GEMINI_API_KEY');
  console.log('2. Run: npx expo start');
  console.log('3. Test on iOS/Android device or simulator');
  console.log('4. Test all editing features (retouch, crop, adjust, filters)');
  console.log('5. Test export and sharing functionality');
} else {
  console.log('\n⚠️ Some tests failed. Please address the issues above before proceeding.');
}

console.log('\n🔗 Useful commands:');
console.log('- npx expo start          # Start development server');
console.log('- npx expo start --clear  # Start with cleared cache');
console.log('- npx expo install --fix  # Fix package versions');
console.log('- npx expo doctor         # Check for common issues');
