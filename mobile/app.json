{"expo": {"name": "Pixshop Mobile", "slug": "pixshop-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "dark", "newArchEnabled": true, "scheme": "pixshop", "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#000000"}, "ios": {"supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "Pixshop needs access to your camera to take photos for editing.", "NSPhotoLibraryUsageDescription": "Pixshop needs access to your photo library to select images for editing.", "NSPhotoLibraryAddUsageDescription": "Pixshop needs permission to save edited photos to your photo library."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#000000"}, "permissions": ["android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.READ_MEDIA_IMAGES"], "edgeToEdgeEnabled": true}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-router", ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you edit them.", "cameraPermission": "The app accesses your camera to let you take photos for editing."}], ["expo-media-library", {"photosPermission": "Allow Pixshop to access your photos.", "savePhotosPermission": "Allow Pixshop to save photos."}], "expo-secure-store"]}}